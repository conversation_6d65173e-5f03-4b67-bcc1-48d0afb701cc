@echo off
echo 🤖 Agent Orchestration Streamlit Demo Launcher
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python 3.11 or higher
    pause
    exit /b 1
)

echo ✅ Python is available

REM Check if streamlit_demo_app.py exists
if not exist "streamlit_demo_app.py" (
    echo ❌ Error: streamlit_demo_app.py not found
    echo Please ensure you're running this script from the correct directory
    pause
    exit /b 1
)

echo ✅ Demo files found

REM Install dependencies if needed
echo 📦 Installing/checking dependencies...
python -m pip install -r requirements_streamlit.txt

if errorlevel 1 (
    echo ⚠️ Warning: Some dependencies might not have installed correctly
    echo You can continue, but the demo might not work properly
    pause
)

echo.
echo 🔍 Checking if services are running...
echo.
echo ⚠️ IMPORTANT: Make sure the following services are running:
echo    1. EmailClassifyingAgent (port 5000): python agent1_uc1.py
echo    2. ConfidenceScoreAgent (port 6010): python agent2_uc1.py  
echo    3. MappingAgent (port 7000): python agent3_uc1.py
echo    4. Orchestration service (port 8000): python main_run_uc1.py
echo.

set /p continue="Do you want to continue? (y/N): "
if /i not "%continue%"=="y" (
    echo Demo cancelled. Please start the required services first.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting Streamlit Demo...
echo The demo will open in your default browser at: http://localhost:8501
echo.
echo Press Ctrl+C to stop the demo
echo.

REM Start Streamlit
python -m streamlit run streamlit_demo_app.py

echo.
echo 👋 Demo stopped. Thank you for using the Agent Orchestration Demo!
pause
