# Teal Agents Framework Tutorial 01
## Configuring a Chat Agent

All agents are configured using a YAML configuration file. For a very simple
agent that simply interacts with the user using a specified LLM, your agent
config file might look something like:

### Example Configuration

```yaml
apiVersion: skagents/v1
kind: Chat
description: >
  A simple chat agent
service_name: ChatBot
version: 0.1
input_type: BaseInput
spec:
  agent:
    name: default
    role: Default Agent
    model: gpt-4o-mini
    system_prompt: >
      You are a helpful assistant.
```


An agent configuration file can contain the following elements:
* apiVersion - At present, this should always be `skagents/v1`
* kind - The way in which an agent will execute its tasks. Currently only the
  value `Chat` and `Sequential` is supported. The current example shows the `chat` style agent.

  With chat-only, there are no tasks in which
  additional instructions can be provided.
* description (optional) - A description of the agent
* service_name - The name of the agent (and thus its service). This, in
  combination with the version will make up the agent's REST and streaming
  endpoints. In this example, said endpoints would be
  * `/ChatBot/0.1`
  * `/ChatBot/0.1/stream`
* version - The version of the agent (see above)
* input_type - The payload format for requests to this agent. For the above
  example, chat-only agent expects input of one of the following types:
    * `BaseInput` - A simple text input
    * `BaseInputWithUserContext` - A text input with user context
    * `BaseMultiModalInput` - Input supporting both images and text

* output_type (optional - not shown) - The payload format for responses from
  this agent (more on this later)
* spec - Agent and task configuration
* agents - A list of agents that can be used by tasks
  * name - The name of the agent
  * role - The role/description of the agent
  * model - The LLM model to use
  * system_prompt - A system prompt for the agent

 A chat-only agent will use the chat history that's provided and attempt to perform any requested actions.