apiVersion: skagents/v1
kind: Sequential
description: >
  An agent for getting and returning vulnerability reports from Microsoft vulnerability
  api
service_name: VulnerabilityAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-mini-2024-07-18
      system_prompt: >
        if a user asks for a list of vulnerabilites only return exactly what is returned by the function
        Format the response as:

        ## Vulnerability Report for {month} {year}
        **Title**: {Title}
        **ID**: [{ID}]({url})

        **Title**: {Title}
        **ID**: [{ID}]({url})

        **Title**: {Title}
        **ID**: [{ID}]({url})

        if title is none, then instead return information for the previous month data
        if multiple months are requested group by month
        at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-mini-2024-07-18
      plugins:
      - VulnerabilityPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need. First use the
        function get_current_month_date to determine the current month and year.
        Then use the function get_security_report to get the vulnerability report
      agent: default
