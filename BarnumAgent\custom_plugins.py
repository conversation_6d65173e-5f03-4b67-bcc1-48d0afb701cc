import pika
import json
import uuid
import os
from redis import Redis
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin


class BarnumPlugin(BasePlugin):
    default_message = {"result": True, "response": "success", "url": "", "guid": ""}

    qa_base_url = os.environ.get("TA_INTERNAL_QA_URL")
    rabbitmq_host = os.environ.get("TA_RABBITMQ_PORT")

    def post_url_queue(self, url):
        """
        This function is responsible for sending a URL
        to a RabbitMQ queue for processing.

        Args:
            url (str): The URL to be processed.

        Returns:
            dict: A dictionary containing the status of the
            operation and additional information.
        """
        message = self.default_message.copy()
        new_guid = str(uuid.uuid4())

        try:
            # Establish a connection to the RabbitMQ server
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(host=self.rabbitmq_host)
            )
            channel = connection.channel()

            # Declare the queue to send the message to
            channel.queue_declare(queue="event_queue")

            event_data = {"job": new_guid, "url": url}

            # Publish the message to the queue
            channel.basic_publish(
                exchange="", routing_key="event_queue", body=json.dumps(event_data)
            )
            message["url"] = url
            message["guid"] = new_guid

        except Exception as e:
            message["result"] = False
            message["response"] = f"error encounter: {e}"
            message["url"] = url

        finally:
            if connection:
                connection.close()

        return message

    @staticmethod
    def retrieve_from_cache(guid):
        """
        Retrieves the processed data from the Redis
        cache using the provided GUID.

        Args:
            guid (str): The unique identifier (GUID) of the job
            for which the processed data needs to be retrieved.

        Returns:
            dict: The 'hits' key from the processed data dictionary.
        """
        redis_host = os.environ.get("TA_REDIS_HOST")
        redis_port = os.environ.get("TA_REDIS_PORT")
        redis_db = os.environ.get("TA_REDIS_DB")
        redis_client = Redis(host=redis_host, port=redis_port, db=redis_db)
        not_processed = True
        processed_data = ""
        while not_processed:
            data = redis_client.get(guid)
            if data:
                processed_data = json.loads(data)
                redis_client.delete(guid)
                not_processed = False
        response = processed_data["hits"]
        return response

    @staticmethod
    def format_final_results(data):
        """
        Formats the input data by creating a new list of dictionaries. The
        return objects from Barnum are rather large so we need to trim them to
        only the needed fields

        Args:
            data (list): A list of dictionaries containing question data.

        Returns:
            list: A new list containing dictionaries with the keys
            'question_title', 'number_of_answers', and '_id'.
            The function stops processing after 10 items.
        """
        final_array = []
        for index, item in enumerate(data):
            if index >= 10:  # Stop after processing 10 items
                break

            new_item = {
                "question_title": item["questionTitle"],
                "number_of_answers": item["numAnswers"],
                "_id": item["_id"],
            }
            final_array.append(new_item)
        return final_array

    @kernel_function(description="query barnum api for questions about a value")
    def get_barnum(self, text: str):
        url = f"{self.qa_base_url}{text}"
        try:
            queue_data = self.post_url_queue(url)
            if queue_data["result"] is True:
                data = self.retrieve_from_cache(queue_data["guid"])
                response = self.format_final_results(data)
                return response
            else:
                return queue_data
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"error encounter: {e}",
                "url": url,
            }
            return message
