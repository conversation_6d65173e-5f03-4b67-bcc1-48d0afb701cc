apiVersion: skagents/v1
kind: Sequential
description: >
  An agent answering all travel questions by interacting with a rag pipeline
  built onto of our search rag pipeline an returns the top 10 results

service_name: TravelAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-05-13
      system_prompt: >
        Do not modify any values in each item
        with one line between each item format each entry as:

        Question: {Iitle}
        URL: {URL}

        Do not format the urls in each item
        at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-2024-05-13
      plugins:
        - TravelPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default
