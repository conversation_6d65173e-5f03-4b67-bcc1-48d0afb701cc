import os
import sys
import json
import unittest
import textwrap
from unittest.mock import patch, mock_open, MagicMock

# --- Begin: Setup logger module mocking with print statements ---
class PrintLogger:
    def info(self, message):
        print("INFO:", message)

    def debug(self, message):
        print("DEBUG:", message)

    def error(self, message):
        print("ERROR:", message)

# Insert mock modules into sys.modules so that the import in custom_plugins.py won't fail.
sys.modules['logger'] = MagicMock()
sys.modules['logger.agent_loggers'] = MagicMock()
sys.modules['logger.agent_loggers'].Logger = PrintLogger
# --- End: Setup logger module mocking with print statements ---

# Ensure our parent directory is in sys.path for module imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, ".."))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from custom_plugins import ConfidenceScorePlugin

class TestConfidenceScorePlugin(unittest.TestCase):
    """
    Unit tests for the ConfidenceScorePlugin class.

    This test suite verifies the functionality of the ConfidenceScorePlugin, 
    which is responsible for extracting and processing confidence scores from 
    email data. The tests cover the following key functionalities:

    1. **Mapping Rules Retrieval**:
       - `test_get_mapping_rules_returns_correct_mapping`: Validates that the 
         `get_mapping_rules` method correctly converts a YAML mapping file 
         into the expected JSON format, ensuring that the mapping rules are 
         accurately represented.

    . **Notification Status Processing**:
       - `test_process_notification_status_with_manual_review_true`: Tests the 
         `process_notification_status` method to confirm that when 
         `manual_review_needed` is set to True, the `notification_sent` flag 
         is set to False for the processed emails.
       - `test_process_notification_status_with_manual_review_false`: Checks 
         that when `manual_review_needed` is False, the `notification_sent` 
         flag is set to True for the processed emails.

    Each test method uses mocking to simulate file reading and to isolate 
    the functionality of the ConfidenceScorePlugin, ensuring that the tests 
    are reliable and do not depend on external files or states.
    """
    def test_get_mapping_rules_returns_correct_mapping(self):
        """
        Test that get_mapping_rules returns the correct JSON conversion of the YAML mapping file.
        """
        # Sample mapping YAML string based on your provided format.
        sample_mapping_yaml = textwrap.dedent("""
            order_extraction_rules:
              description: "Reglas para extraer datos importantes del pedido del contenido del correo electrónico."
              rules:
                - field: "from"
                  description: "Nombre y correo electrónico del remitente."
                  extraction_method: "Extraer el nombre y el correo electrónico del remitente del campo 'De:'."
                  subfields:
                    - name: "name"
                      extraction_method: "Extraer el nombre antes de la dirección de correo electrónico."
                    - name: "email"
                      extraction_method: "Extraer la dirección de correo electrónico entre corchetes angulares."
                - field: "order_details"
                  description: "Información clave sobre el pedido."
                  extraction_method: "Extraer los detalles del pedido del contenido del correo electrónico."
                  subfields:
                    - name: "order_number"
                      extraction_method: "Extraer el número de pedido del campo relevante."
                    - name: "order_date"
                      extraction_method: "Extraer la fecha del pedido del campo relevante."
                    - name: "delivery_date"
                      extraction_method: "Extraer la fecha de entrega del campo relevante."
                    - name: "client"
                      extraction_method: "Extraer el nombre del cliente del campo relevante."
                    - name: "destination"
                      extraction_method: "Extraer el destino de entrega del campo relevante."
                    - name: "delivery_address"
                      extraction_method: "Extraer la dirección de entrega del campo relevante."
                    - name: "payer_code"
                      extraction_method: "Extraer el código del pagador del campo relevante."
                    - name: "sold_to_code"
                      extraction_method: "Extraer el código vendido a del campo relevante."
                    - name: "ship_to_code"
                      extraction_method: "Extraer el código de envío a del campo relevante."
                    - name: "total_value"
                      extraction_method: "Extraer el valor total del campo relevante."
                - field: "items"
                  description: "Lista de artículos incluidos en el pedido."
                  extraction_method: "Extraer los detalles de los artículos del contenido del correo electrónico."
                  subfields:
                    - name: "description"
                      extraction_method: "Extraer la descripción del artículo."
                    - name: "quantity"
                      extraction_method: "Extraer la cantidad del artículo."
                    - name: "unit_value"
                      extraction_method: "Extraer el valor unitario del artículo."
                    - name: "total_value"
                      extraction_method: "Extraer el valor total del artículo."
        """)

        with patch('builtins.open', mock_open(read_data=sample_mapping_yaml)) as mocked_file:
            plugin = ConfidenceScorePlugin()
            result = plugin.get_mapping_rules()
            mocked_file.assert_called_with(plugin.mapping_file, "r", encoding="utf-8")
            # Load expected mapping from YAML for comparison.
            import yaml
            expected_mapping = yaml.safe_load(sample_mapping_yaml)
            self.assertEqual(json.loads(result), expected_mapping)

    def test_process_notification_status_with_manual_review_true(self):
        """
        Test that process_notification_status sets notification_sent to False when manual_review_needed is True.
        """
        # Sample input JSON for process_notification_status with one email having manual_review_needed=true.
        sample_input_data = {
            "emails": [
                {
                    "email_id": 1,
                    "average_confidence_score": 91.60,
                    "manual_review_needed": True,
                    "mapped_fields": [
                        {
                            "key": "From",
                            "value": "Garzon Munoz, Kevin Steven <<EMAIL>>",
                            "confidence_score": 95.35
                        },
                        {
                            "key": "Sent",
                            "value": "Tuesday, May 6, 2025 1:47 AM",
                            "confidence_score": 95.60
                        },
                        {
                            "key": "To",
                            "value": "ColombiaBSCSO",
                            "confidence_score": 91.02
                        },
                        {
                            "key": "Subject",
                            "value": "Orden de Compra 13373397 - BOGOTA-MERCK SHARP & DOHME 2025-05-09",
                            "confidence_score": 89.91
                        }
                    ]
                }
            ]
        }
        sample_input_json = json.dumps(sample_input_data)
        plugin = ConfidenceScorePlugin()
        result = plugin.process_notification_status(sample_input_json)
        processed_result = json.loads(result)
        # We expect a list of emails; check that the notification_sent flag is False.
        self.assertIsInstance(processed_result, list)
        for email in processed_result:
            self.assertEqual(email.get("notification_sent"), False)
            self.assertEqual(email.get("manual_review_needed"), True)

    def test_process_notification_status_with_manual_review_false(self):
        """
        Test that process_notification_status sets notification_sent to True when manual_review_needed is False.
        """
        # Sample input where manual_review_needed is false.
        sample_input_data = {
            "emails": [
                {
                    "email_id": 2,
                    "average_confidence_score": 95.00,
                    "manual_review_needed": False,
                    "mapped_fields": [
                        {
                            "key": "From",
                            "value": "Example Sender <<EMAIL>>",
                            "confidence_score": 98.00
                        }
                    ]
                }
            ]
        }
        sample_input_json = json.dumps(sample_input_data)
        plugin = ConfidenceScorePlugin()
        result = plugin.process_notification_status(sample_input_json)
        processed_result = json.loads(result)
        # We expect a list of emails; check that the notification_sent flag is True.
        self.assertIsInstance(processed_result, list)
        for email in processed_result:
            self.assertEqual(email.get("notification_sent"), True)
            self.assertEqual(email.get("manual_review_needed"), False)

if __name__ == '__main__':
    unittest.main(verbosity=2)