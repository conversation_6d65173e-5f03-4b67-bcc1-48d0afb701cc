# IdentifyDefectAgent README
 
## 1. Overview
The **IdentifyDefectAgent** processes Celonis data, analyzes it for defects, and classifies any identified defects based on predefined rules.
 
## 2. Features
- Analyzes Celonis data to identify defects.
- Utilizes a set of classification rules defined in a YAML file.
- Returns results as a structured JSON array containing identified defects.
 
## 3. Dependencies
 
This agent depends on the following files:
- **Configuration File:** `IdentifyDefectAgent/config.yaml`
- **Plugin File:** `IdentifyDefectAgent/custom_plugins.py`
- **Rules File:** `../sample/defect_rules.yaml`
- **Agent file:** `../agent1_uc2.py`
 
### Environment Variables
 
Create a `.env` file at `IdentifyDefectAgent/.env` with the following required values:
 
```plaintext
TA_API_KEY="Use your KEY here"
TA_SERVICE_CONFIG=IdentifyDefectAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=misc/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
```
 
## 4. How the Agent Works
 
The agent operates through the following workflow:
 
1. **Configuration Loading:**  
   Loads configuration from `IdentifyDefectAgent/config.yaml`, which defines its structure, input types, and processing agents.
 
2. **Plugin Initialization:**  
   Imports `custom_plugins.py`, where the `IdentifyDefectPlugin` class is defined. This class initializes necessary parameters and loads classification rules from the rules file.
 
3. **Defect Identification:**  
   When Celonis data is received, the `identify_defects` function is called within the `IdentifyDefectPlugin`. This function:
   - Analyzes the input data based on the classification rules.
   - Constructs a prompt for the Azure OpenAI model to identify defects.
   - Calls the OpenAI API with the constructed prompt.
 
4. **Result Generation:**  
   Returns the identification result in a structured JSON format, including the identified defects and their descriptions.
 
5. **Logging:**  
   A log file is created in the `/sample/` directory for each run, named with the format `agent_log_YYYY-MM-DD_HH-MM-SS` (e.g., `agent_log_2025-05-29_12-43-57`).
 
## 5. Usage
 
To run the agent, follow these steps:
 
1. Navigate to the main folder.
2. Activate the virtual environment by running the command:
  ```bash
  .venv/scripts/activate
  ```
3. Run the agent:
   ```bash
   python agent1_uc2.py
   ```
 
## 6. Sample Output
 
Here is a sample output from the last successful run of the IdentifyDefectAgent:
 
```json
[
    {
      "_CASE_KEY": "1109330510946374720240010208",
      "_INVOICE_KEY": "110933051094637472024001",
      "VENDOR_CODE": "0003097722",
      "PAYMENT_METHOD": "M",
      "RISK_TEXT": "Payment Method on the Invoice and Vendor Master Data does not match.",
      "AGENT_IDENTIFIED_RISK": "The payment method has changed, indicating a potential risk of payment processing discrepancies.",
      "AGENT_COMMNET": "Invoice 35765 is impacted due to a change in payment method. The previous payment method was not provided, and the new payment method is M. This change may lead to discrepancies in payment processing."
    }
]
```
 
## 7. Unit Testing
 
To run the unit tests:
 
1. Navigate to the `IdentifyDefectAgent` folder.
2. Execute the following command in the terminal:
   ```bash
   python -m unittest -v .\tests\test_custom_plugins.py
   ```
  
## 8. API Access
 
Once running, access the Swagger documentation at:  
[http://localhost:8110/IdentifyDefectAgent/0.1/docs](http://localhost:8110/IdentifyDefectAgent/0.1/docs)