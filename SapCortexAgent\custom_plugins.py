import os
import io
import concurrent.futures
from openai import AzureOpenAI
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin
from sk_agents.extra_data_collector import ExtraDataCollector
import requests
import pypdfium2 as pdfium


class SapCortexPlugin(BasePlugin):
    def __init__(
        self,
        authorization: str | None = None,
        extra_data_collector: ExtraDataCollector | None = None,
    ):
        super().__init__(authorization, extra_data_collector)
        self.api_base_url = os.environ.get("TA_SEARCH_URL")
        self.api_key_name = os.environ.get("TA_API_GATEWAY_KEY_NAME")
        self.api_key = os.environ.get("TA_SEARCH_API_KEY")
        self.token = authorization
        self.internal_ai_model = "gpt-4o-mini-2024-07-18"

    def call_search_api(self, text, rowlimit):
        try:
            url = self.api_base_url
            headers = {
                "X-SH-Authorization": f"Bearer {self.token}",
                self.api_key_name: self.api_key,
            }
            payload = {
                "QueryText": text,
                "TrimDuplicates": "true",
                "QueryTemplate": "{searchboxquery} NOT ESC_PARENT_ESC_TITLE:\"RETIRED\" FederatorBackends:\"HhiRagSAPCortex\"",
                "SortList": [{"SortType": 1, "PropertyName": "Rank", "Direction": 1}],
                "EnableSorting": "true",
                "RowLimit": rowlimit,
                "StartRow": 0,
                "SelectProperties": [
                    "ESC_PARENT_ESC_TITLE",
                    "ESC_body",
                    "ESC_PARENT_ESCBASE_URL",
                ],
            }
            
            response = requests.post(url, headers=headers, data=payload)
            print(response)
            response.raise_for_status()
            results = response.json()
            return results
        except Exception as e:
            print(e)
            return e

    def process_item(self, item, text):
        try:
            text_body = item["text_body"]
            summary = self.call_openai(text, text_body)
            return {
                "Title": item["Properties"][1]["Value"],
                "Url": item["Properties"][2]["Value"],
                "Summary": summary,
            }
        except Exception as e:
            print(f"An error occurred while processing item: {e}")
            return None

    def get_document_text(self, items):
        document_results = []
        for item in items:
            url = item["Properties"][2]["Value"]
            print("downloading text")
            text_body = self.download_and_extract_text(url)
            print("text found")
            item["text_body"] = text_body
            document_results.append(item)
        return document_results

    def parse_search_data(self, results, text):
        try:
            parsed_results = []
            search_results = results.get("RelevantResults", [])
            document_results = self.get_document_text(search_results)
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(self.process_item, item, text)
                    for item in document_results
                ]
                for future in concurrent.futures.as_completed(futures):
                    result = future.result()
                    if result:
                        parsed_results.append(result)

            return parsed_results
        except Exception as e:
            print(f"An error occurred: {e}")

    def call_openai(self, text, body):
        prompt = [
            {
                "role": "system",
                "content": f"generate a result with the content relevant to {text}.",
            },
            {"role": "user", "content": body},
        ]
        print("calling AI")
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        completion = client.beta.chat.completions.parse(
            model=self.internal_ai_model,
            messages=prompt,
            temperature=0,
        )
        return completion.choices[0].message.content

    def download_and_extract_text(self, url):
        try:
            # Download the file
            response = requests.get(url, stream=True)
            # Check the content type
            content_type = response.headers["Content-Type"]
            if content_type != "application/pdf":
                raise ValueError("The provided URL does not link to a PDF file.")

            # Use BytesIO to treat the downloaded bytes as a file
            pdf_file = io.BytesIO(response.content)
            # Create a PDF reader object
            pdf_reader = pdfium.PdfDocument(pdf_file)

            # Extract text from each page
            pdf_text = ""
            for page in pdf_reader:
                page_text = page.get_textpage()
                text_all = page_text.get_text_range()
                pdf_text += text_all   # Add newline for page breaks
            return (
                pdf_text.strip()
            )  # Return extracted text without leading/trailing whitespace
        except Exception as e:
            print(f"An error occurred: {e}")

    def get_unique_values_from_list_of_dicts(self, list_of_dicts, key):
        unique_values = []
        seen_values = set()
        for dictionary in list_of_dicts:
            if key in dictionary:
                value = dictionary[key]
                if value not in seen_values:
                    unique_values.append(value)
                    seen_values.add(value)
                 
        return unique_values
    
    def find_first_dict_by_key_value(self, list_of_dicts, key, value):
        for dictionary in list_of_dicts:
            if key in dictionary and dictionary[key] == value:
                return dictionary
        return None

    @kernel_function(description="query search for SAP information.")
    def get_info(self, text: str):
        try:
            data = self.call_search_api(text, 2)

            list_of_parsed_data = self.parse_search_data(data, text)
            
            #remove results parsed from the same documents and checking for edge case.
            unique_url = self.get_unique_values_from_list_of_dicts(list_of_parsed_data, 'Url')
            unique_title = self.get_unique_values_from_list_of_dicts(list_of_parsed_data, 'Title')
            if len(unique_url) <= len(unique_title):
                parsed_data = [self.find_first_dict_by_key_value(list_of_parsed_data,"Url",x) for x in unique_url]
            else:
                parsed_data = [self.find_first_dict_by_key_value(list_of_parsed_data,"Title",x) for x in unique_title]

            if parsed_data:
                return parsed_data
            else:
                return "no results were found, please try adjusting your prompt"
        except Exception as e:
            print(f"error encounter: {e}")
            raise e
