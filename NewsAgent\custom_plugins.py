import os
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin
from ska_utils import StandardDates
from datetime import datetime
import httpx
import asyncio


class NewsPlugin(BasePlugin):
    api_base_url = os.environ.get("TA_SEARCH_URL")
    api_key = os.environ.get("TA_SEARCH_API_KEY")
    api_version = os.environ.get("TA_SEARCH_API_VERSION")
    api_key_name = os.environ.get("TA_API_GATEWAY_KEY_NAME")

    async def call_search_api(self, text, start_date: str = None):
        url = f"{self.api_base_url}{self.api_version}"
        headers = {self.api_key_name: self.api_key}
        payload = {
            "search": text,
            "searchMode": "any",
            "select": (
                "ESC_body, "
                "ESC_PARENT_ESCBASE_TITLE, "
                "ESC_PARENT_ESCBASE_URL, "
                "ESC_PublishedDate, "
                "ESC_PARENT_ESCBASE_ID"
            ),
            "queryType": "full",
            "count": True,
            "top": "50",
        }
        async with httpx.AsyncClient() as client:
            if start_date:
                payload["filter"] = f"ESC_PublishedDate ge {start_date}"

            response = await client.post(
                url=url,
                headers=headers,
                data=payload
            )
            response.raise_for_status()
            data = response.json()
            return data

    async def parse_search_data(self, data):
        parsed_results = []
        search_results = data.get("value", [])
        ids_found = []
        for index, item in enumerate(search_results):
            if index >= 50:  # Stop after processing 10 items
                break
            published_datetime = datetime.fromisoformat(
                item["ESC_PublishedDate"]
            )
            new_item = {
                "Title": item["ESC_PARENT_ESCBASE_TITLE"],
                "Body": item["ESC_body"],
                "Url": item["ESC_PARENT_ESCBASE_URL"],
                "Published": published_datetime.strftime("%m/%d/%Y"),
            }
            if item["ESC_PARENT_ESCBASE_ID"] not in ids_found:
                new_item['Url'] = str(new_item['Url']).replace(",", "%2C")
                ids_found.append(item["ESC_PARENT_ESCBASE_ID"])
                parsed_results.append(new_item)

            if len(ids_found) >= 10:
                break
        return parsed_results

    @kernel_function(
        description=(
            "query search for news, "
            "if start date is included it needs "
            "to be formatted as 2025-01-01T00:00:00Z"
        )
    )
    async def get_news(self, text: str, start_date: str = None):
        try:
            data = await self.call_search_api(text, start_date)
            print(data)
            parsed_data = await self.parse_search_data(data)
            if parsed_data:
                return parsed_data
            else:
                return "no results found"
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"error encounter: {e}",
            }
            return message

    @kernel_function(
        description=(
            "returns current month and year "
            "that can be used to generate "
            "a start date for get_news"
        )
    )
    async def get_current_month_date(self):
        dates = StandardDates()
        month = dates.get_current_month_full_name()
        year = dates.get_current_year()
        day = dates.get_date()
        return month, year, day
