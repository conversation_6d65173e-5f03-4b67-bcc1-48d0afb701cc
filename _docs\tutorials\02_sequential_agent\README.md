# Teal Agents Framework Tutorial 02
## Configuring a Sequential Agent

In addition to Chat agents, there is also Sequential type agents. This means for each invocation, the agent will execute all tasks in the defined order.

```yaml
apiVersion: skagents/v1
kind: Sequential
description: >
  A simple chat agent
service_name: ChatBot
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o
      system_prompt: >
        You are a helpful assistant.
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default
```

Sequential agents configuration file can contain these additional following elements:

* tasks - A list of tasks to be executed by the agent
  * name - The name of the task
  * task_no - The order in which the task should be executed
  * description - A description of the task
  * instructions - Instructions for the task
  * agent - The agent to use for the task (must match agent defined in the
    agents section)