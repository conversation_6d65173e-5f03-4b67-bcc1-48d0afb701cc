import streamlit as st
import requests
import json
import pandas as pd
from typing import Dict, Any, Optional, List
import email

# Configure Streamlit page
st.set_page_config(
    page_title="Agent Orchestration Demo",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-status {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .agent-pending {
        background-color: #f0f0f0;
        border-left: 4px solid #cccccc;
    }
    .agent-processing {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
    }
    .agent-completed {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }
    .agent-failed {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
    }
    .json-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

class EmailProcessor:
    """Utility class for processing uploaded email files and attachments."""
    
    @staticmethod
    def process_email_file(uploaded_file) -> Dict[str, Any]:
        """Process uploaded email file and extract key-value pairs."""
        try:
            if uploaded_file.type == "message/rfc822" or uploaded_file.name.endswith('.eml'):
                return EmailProcessor._process_eml_file(uploaded_file)
            elif uploaded_file.name.endswith('.txt'):
                return EmailProcessor._process_txt_file(uploaded_file)
            else:
                # For demo purposes, create a sample structure
                return EmailProcessor._create_sample_email_data(uploaded_file.name)
        except Exception as e:
            st.error(f"Error processing email file: {str(e)}")
            return EmailProcessor._create_sample_email_data(uploaded_file.name)
    
    @staticmethod
    def _process_eml_file(uploaded_file) -> Dict[str, Any]:
        """Process .eml email file."""
        content = uploaded_file.read().decode('utf-8')
        msg = email.message_from_string(content)
        
        email_data = []
        
        # Extract basic headers
        headers = ['From', 'To', 'Subject', 'Date']
        for header in headers:
            if msg.get(header):
                email_data.append({
                    "key": f"{header}:",
                    "Key_confidence_score": 95.0,
                    "value": msg.get(header),
                    "Value_confidence_score": 95.0
                })
        
        # Extract body content and create key-value pairs
        body = EmailProcessor._get_email_body(msg)
        if body:
            # Simple extraction - in real implementation, this would use NLP/OCR
            lines = body.split('\n')
            for line in lines[:10]:  # Limit to first 10 lines for demo
                if ':' in line and len(line.strip()) > 0:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        email_data.append({
                            "key": parts[0].strip(),
                            "Key_confidence_score": 85.0,
                            "value": parts[1].strip(),
                            "Value_confidence_score": 85.0
                        })
        
        return {"email_data": email_data}
    
    @staticmethod
    def _process_txt_file(uploaded_file) -> Dict[str, Any]:
        """Process .txt email file."""
        content = uploaded_file.read().decode('utf-8')
        email_data = []
        
        lines = content.split('\n')
        for line in lines:
            if ':' in line and len(line.strip()) > 0:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    email_data.append({
                        "key": parts[0].strip(),
                        "Key_confidence_score": 85.0,
                        "value": parts[1].strip(),
                        "Value_confidence_score": 85.0
                    })
        
        return {"email_data": email_data}
    
    @staticmethod
    def _get_email_body(msg) -> str:
        """Extract body from email message."""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    return part.get_payload(decode=True).decode('utf-8')
        else:
            return msg.get_payload(decode=True).decode('utf-8')
        return ""
    
    @staticmethod
    def _create_sample_email_data(filename: str) -> Dict[str, Any]:
        """Create sample email data for demo purposes."""
        return {
            "email_data": [
                {
                    "key": "From:",
                    "Key_confidence_score": 95.0,
                    "value": "<EMAIL>",
                    "Value_confidence_score": 95.0
                },
                {
                    "key": "Subject:",
                    "Key_confidence_score": 95.0,
                    "value": f"Demo Email - {filename}",
                    "Value_confidence_score": 95.0
                },
                {
                    "key": "Order Number:",
                    "Key_confidence_score": 90.0,
                    "value": "ORD-12345",
                    "Value_confidence_score": 90.0
                }
            ]
        }
    
    @staticmethod
    def process_attachment_file(uploaded_file) -> List[Dict[str, Any]]:
        """Process uploaded attachment file (CSV)."""
        try:
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
                attachment_data = []
                
                for _, row in df.iterrows():
                    item = {}
                    for col in df.columns:
                        if pd.api.types.is_numeric_dtype(df[col]):
                            item[col] = float(row[col]) if not pd.isna(row[col]) else 0.0
                        else:
                            item[col] = str(row[col]) if not pd.isna(row[col]) else ""
                    attachment_data.append(item)
                
                return attachment_data
            else:
                # For non-CSV files, create sample attachment data
                return EmailProcessor._create_sample_attachment_data(uploaded_file.name)
        except Exception as e:
            st.error(f"Error processing attachment file: {str(e)}")
            return EmailProcessor._create_sample_attachment_data(uploaded_file.name)
    
    @staticmethod
    def _create_sample_attachment_data(filename: str) -> List[Dict[str, Any]]:
        """Create sample attachment data for demo purposes."""
        return [
            {
                "key": "Document Type:",
                "Key_confidence_score": 95.0,
                "value": "Purchase Order",
                "Value_confidence_score": 95.0
            },
            {
                "key": "Attachment Name:",
                "Key_confidence_score": 95.0,
                "value": filename,
                "Value_confidence_score": 95.0
            }
        ]

class APIClient:
    """Client for making API calls to the agent orchestration service."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def check_service_health(self) -> bool:
        """Check if the orchestration service is running."""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def process_email(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send email data to the orchestration service for processing."""
        try:
            # Create the input structure expected by the API
            input_data = {
                "input_text": {
                    "email_id": "demo_upload",
                    "email_data": email_data.get("email_data", []),
                    "attachment_data": email_data.get("attachment_data", [])
                }
            }
            
            response = requests.post(
                f"{self.base_url}/master/agent",
                json=input_data,
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API call failed: {str(e)}")

def display_agent_status(agent_name: str, status: str, response_data: Optional[Dict] = None):
    """Display the status of an agent in the workflow."""
    status_class = f"agent-{status}"
    
    if status == "pending":
        icon = "⏳"
        status_text = "Pending"
    elif status == "processing":
        icon = "🔄"
        status_text = "Processing..."
    elif status == "completed":
        icon = "✅"
        status_text = "Completed"
    elif status == "failed":
        icon = "❌"
        status_text = "Failed"
    else:
        icon = "❓"
        status_text = "Unknown"
    
    st.markdown(f"""
    <div class="agent-status {status_class}">
        <h4>{icon} {agent_name}</h4>
        <p><strong>Status:</strong> {status_text}</p>
    </div>
    """, unsafe_allow_html=True)
    
    if response_data and status == "completed":
        with st.expander(f"View {agent_name} Response"):
            st.json(response_data)

def format_json_response(data: Dict[str, Any]) -> str:
    """Format JSON response for better readability."""
    return json.dumps(data, indent=2, ensure_ascii=False)

def main():
    """Main Streamlit application."""
    st.markdown('<h1 class="main-header">🤖 Agent Orchestration Demo</h1>', unsafe_allow_html=True)

    st.markdown("""
    This demo application showcases the agent orchestration workflow for email processing.
    Upload an email file and optional attachments to see how the three agents work together:

    1. **EmailClassifyingAgent** - Classifies the email content
    2. **ConfidenceScoreAgent** - Calculates confidence scores (if classified as order)
    3. **MappingAgent** - Maps data to target format (if confidence > 85%)
    """)

    # Initialize API client
    api_client = APIClient()

    # Check service health
    st.sidebar.header("Service Status")
    if api_client.check_service_health():
        st.sidebar.success("✅ Orchestration service is running")
        service_available = True
    else:
        st.sidebar.error("❌ Orchestration service is not available")
        st.sidebar.info("Please ensure the service is running at http://localhost:8000")
        service_available = False

    # File upload section
    st.header("📁 Upload Files")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Email File")
        email_file = st.file_uploader(
            "Upload email file",
            type=['eml', 'msg', 'txt'],
            help="Supported formats: .eml, .msg, .txt"
        )

    with col2:
        st.subheader("Attachment Files (Optional)")
        attachment_files = st.file_uploader(
            "Upload attachment files",
            type=['csv', 'pdf', 'xlsx'],
            accept_multiple_files=True,
            help="Supported formats: .csv, .pdf, .xlsx"
        )

    if email_file is not None:
        # Process uploaded files
        st.header("📊 File Processing")

        with st.spinner("Processing uploaded files..."):
            # Process email file
            email_data = EmailProcessor.process_email_file(email_file)

            # Process attachment files
            attachment_data = []
            if attachment_files:
                for attachment_file in attachment_files:
                    attachment_data.extend(EmailProcessor.process_attachment_file(attachment_file))

            # Combine data
            combined_data = {
                "email_data": email_data.get("email_data", []),
                "attachment_data": attachment_data
            }

        # Display processed data
        st.success("✅ Files processed successfully!")

        # Create two columns for side-by-side view
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("📧 Original Content")

            st.write("**Email Data:**")
            if email_data.get("email_data"):
                email_df = pd.DataFrame(email_data["email_data"])
                st.dataframe(email_df, use_container_width=True)
            else:
                st.info("No email data extracted")

            if attachment_data:
                st.write("**Attachment Data:**")
                attachment_df = pd.DataFrame(attachment_data)
                st.dataframe(attachment_df, use_container_width=True)
            else:
                st.info("No attachment data")

        with col2:
            st.subheader("🤖 Agent Processing Results")

            if service_available:
                if st.button("🚀 Start Agent Processing", type="primary"):
                    # Initialize session state for tracking
                    if 'processing_started' not in st.session_state:
                        st.session_state.processing_started = True
                        st.session_state.agent_responses = {}

                    # Create placeholders for agent status
                    agent1_placeholder = st.empty()
                    agent2_placeholder = st.empty()
                    agent3_placeholder = st.empty()

                    try:
                        # Show initial status
                        with agent1_placeholder.container():
                            display_agent_status("EmailClassifyingAgent", "processing")
                        with agent2_placeholder.container():
                            display_agent_status("ConfidenceScoreAgent", "pending")
                        with agent3_placeholder.container():
                            display_agent_status("MappingAgent", "pending")

                        # Make API call
                        with st.spinner("Processing with agents..."):
                            response = api_client.process_email(combined_data)

                        # Update agent statuses based on response
                        agent1_response = response.get("agent1_response")
                        agent2_response = response.get("agent2_response")
                        agent3_response = response.get("agent3_response")

                        # Agent 1 status
                        with agent1_placeholder.container():
                            if agent1_response:
                                display_agent_status("EmailClassifyingAgent", "completed", agent1_response)
                            else:
                                display_agent_status("EmailClassifyingAgent", "failed")

                        # Agent 2 status
                        with agent2_placeholder.container():
                            if agent2_response:
                                display_agent_status("ConfidenceScoreAgent", "completed", agent2_response)
                            elif agent1_response and agent1_response.get("classification") == "order":
                                display_agent_status("ConfidenceScoreAgent", "failed")
                            else:
                                st.info("ℹ️ ConfidenceScoreAgent: Skipped (email not classified as order)")

                        # Agent 3 status
                        with agent3_placeholder.container():
                            if agent3_response:
                                display_agent_status("MappingAgent", "completed", agent3_response)
                            elif agent2_response:
                                # Check confidence score
                                avg_score = 0
                                if isinstance(agent2_response, list) and len(agent2_response) > 0:
                                    avg_score = agent2_response[0].get("average_confidence_score", 0)
                                elif isinstance(agent2_response, dict):
                                    avg_score = agent2_response.get("average_confidence_score", 0)

                                if avg_score <= 85:
                                    st.info(f"ℹ️ MappingAgent: Skipped (confidence score {avg_score} ≤ 85%)")
                                else:
                                    display_agent_status("MappingAgent", "failed")
                            else:
                                st.info("ℹ️ MappingAgent: Skipped (previous agents did not complete)")

                        # Display final results
                        st.subheader("📋 Final Results")
                        st.markdown('<div class="json-container">', unsafe_allow_html=True)
                        st.json(response)
                        st.markdown('</div>', unsafe_allow_html=True)

                        # Success message
                        st.success("🎉 Agent processing completed successfully!")

                    except Exception as e:
                        st.error(f"❌ Error during processing: {str(e)}")

                        # Update all agents to failed status
                        with agent1_placeholder.container():
                            display_agent_status("EmailClassifyingAgent", "failed")
                        with agent2_placeholder.container():
                            display_agent_status("ConfidenceScoreAgent", "failed")
                        with agent3_placeholder.container():
                            display_agent_status("MappingAgent", "failed")
            else:
                st.error("❌ Cannot start processing: Orchestration service is not available")
                st.info("Please ensure the FastAPI service is running at http://localhost:8000")

    # Instructions section
    st.sidebar.header("📖 Instructions")
    st.sidebar.markdown("""
    **How to use this demo:**

    1. **Start the services**: Ensure all agent services are running:
       - EmailClassifyingAgent (port 5000)
       - ConfidenceScoreAgent (port 6010)
       - MappingAgent (port 7000)
       - Orchestration service (port 8000)

    2. **Upload files**:
       - Upload an email file (.eml, .msg, or .txt)
       - Optionally upload attachment files (.csv, .pdf, .xlsx)

    3. **Process**: Click "Start Agent Processing" to begin the workflow

    4. **View results**: Monitor the progress and view the final results
    """)

    st.sidebar.header("🔧 Troubleshooting")
    st.sidebar.markdown("""
    **Common issues:**

    - **Service not available**: Check if `main_run_uc1.py` is running
    - **Agent timeouts**: Ensure individual agents are running on their ports
    - **File format errors**: Use supported file formats only
    """)

if __name__ == "__main__":
    main()
