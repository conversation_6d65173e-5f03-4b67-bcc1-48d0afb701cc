define create_or_update_var_macos
	if [ -f $(1) ]; then \
		if grep -q "^$(2)=" $(1); then \
			sed -i '' "s~^$(2)=.*~$(2)=$$$(2)~" $(1); \
		else \
			echo "$(2)=$$$(2)" >> $(1); \
		fi; \
	else \
		echo "$(2)=$$$(2)" > $(1); \
	fi
endef

define create_or_update_var_bash
	if [ -f $(1) ]; then \
		if grep -q "^$(2)=" $(1); then \
			sed -i "s~^$(2)=.*~$(2)=$$$(2)~" $(1); \
		else \
			echo "$(2)=$$$(2)" >> $(1); \
		fi; \
	else \
		echo "$(2)=$$$(2)" > $(1); \
	fi
endef

.PHONY: all environment-mac environment-bash lint test

all : | environment-mac lint test

environment-mac:
	@echo "Creating .env file..."
	@cp .env.example .env
	@echo "Please enter your TA_API_KEY"
	@read -p "API Key: " TA_API_KEY && \
		$(call create_or_update_var_macos,.env,TA_API_KEY)
	@echo "Updated API key."
	@echo "Please enter your TA_SERVICE_CONFIG e.g. ChatMathAgent/config.yaml"
	@read -p "Agent service configuration path: " TA_SERVICE_CONFIG && \
		$(call create_or_update_var_macos,.env,TA_SERVICE_CONFIG)
	@echo "Updated agent service configuration."
	@echo "Running uv sync..."
	@uv sync --native-tls

environment-bash:
	@echo "Creating .env file..."
	@cp .env.example .env
	@echo "Please enter your TA_API_KEY"
	@read -p "API Key: " TA_API_KEY && \
		$(call create_or_update_var_bash,.env,TA_API_KEY)
	@echo "Updated API key."
	@echo "Please enter your TA_SERVICE_CONFIG e.g. ChatMathAgent/config.yaml"
	@read -p "Agent service configuration path: " TA_SERVICE_CONFIG && \
		$(call create_or_update_var_bash,.env,TA_SERVICE_CONFIG)
	@echo "Updated agent service configuration."
	@uv sync --native-tls

lint:
	@echo "Linting Python code..."
	@uv run black .
	@uv run ruff check --fix

test :
	@uv run -- python agent.py