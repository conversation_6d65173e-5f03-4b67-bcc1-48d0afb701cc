# Barnum Custom Plugin

This plugin handles querying the barnum api. It connects to the
https://barnum.merck.com/question/{value}, and then after retreiving the results it parses the first 10 items from the response and the llm returns them in the format:

```Json
Question Title: string
Number of Answers: integer
Url: 'https://barnum.merck.com/question/{id}'
```

Please note the Architecture of this agent is generally overengineered for its current purpose the Barnum API scales and
handles request fine. Rather the main purpose of this agent is to be a example of a way to build an agent that uses Redis and RabbitMQ to act as a job queuing system that allows for us to restrict how many active concurrent calls or process can potentially happen at a given time

## General Architecture

How the current plugin is design is there are 4 parts:

- config.yaml
- custom_plugins.py
- redis
- rabbitWorker
- rabbitMQ

The general design method is the custom plugin gets called by the llm and then passes the search term to call barnum with to a RabbitMQ message queue. If a worker in RabbitWorker is free, it will grab the item from the queue send the call to barnum then store the results in the standard redis cache. While this is happening the custom plugin code has switch to a loop that waits for the processed job to be visible in the cache. Once the data appears in the cache the custom plugin code pulls the data down, removes it from the cache as not to waste space and then returns it to the llm to generate the final response sent back to the user. You can monitor the RabbitMQ server via the dashboard at:
http://localhost:15672/#/

### Redis

This folder just contains a basic docker image configuring a default redis server

### rabbitWorker

This folder contains the code need to run and build the rabbit worker nodes that will act as a sort of queuing mechanism and handle processing the requests to barnum

### config.yaml

This file contains the needed configurations files for the agent
to run. It also includes the system prompt and the prompts for the tasks the agent will do

### custom_plugin.py

This is the file containing the code you want the agent to be
able to excute when a user is interacting with it.

### RabbitMQ server and Dashboard

The rabbitMQ server that holds the rabbitMQ code and dashboard is just a default image: rabbitmq:3-management configured in the docker compose file. to monitor or interact with the RabbitMQ server you can navigate to http://localhost:15672/#/ to view the server dashboard


### How Barnum was added to Example Orchestrator

- First add a folder named to indicate what the agent is to example. In barnum case
the folder was named "Barnum".

- Next define you config file and your custom_plugin code inside the barnum file
in addition to any other components needed for it to work.

- Then go to the jose folder and in the config.yaml add: agentName:versionNumber
to the list of agents

- Then in kong.yaml add the networking information to the yaml file for your
new agent. First add a new group called: AgentNameConsumers, then add the networking
information to the yaml you can use the existing kong file. Make sure the name
in your connection details matches the service name defined in the config file 
you created in Barnum.

- Then the last piece you need to do is update the docker compose file to create
a docker container for your new service please look at the barnum one as a example
Please be aware that if you agent needs a requirements.txt file you do not have to
include a command to install it, the framework should automatically do that as
part of the build.
