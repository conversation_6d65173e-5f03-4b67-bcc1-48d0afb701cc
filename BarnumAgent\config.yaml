apiVersion: skagents/v1
kind: Sequential
description: >
  An agent for searching Barnum questions
service_name: BarnumAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-05-13
      system_prompt: >
        You are a helpful assistant. when returning data from barnum each entry
        with one line between each item format each entry as:
          Question Title:
          Number of Answers:
          Url: https://barnum.merck.com/question/ + the field _id
        Only parse and return the first 10 results, if less then 10 are found
        only return them, do not add entries from old results.
        If a error is return print out the entire error message without changes
        Do not add anything to the response
        do not add ```<PERSON><PERSON> at the start of the response
        do not add ``` at the end of the respone
        at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-2024-05-13
      plugins:
        - BarnumPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default
