from typing import List
import os
import requests
from pydantic import BaseModel, ConfigDict
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin
from openai import AzureOpenAI


class LocationCoordinates(BaseModel):
    latitude: float
    longitude: float
    timezone: str


class DailyUnits(BaseModel):
    time: str
    temperature_2m_max: str
    temperature_2m_min: str


class Daily(BaseModel):
    time: List[str]
    temperature_2m_max: List[float]
    temperature_2m_min: List[float]


class TemperatureResponseInt(BaseModel):
    latitude: float
    longitude: float
    generationtime_ms: float
    utc_offset_seconds: int
    timezone: str
    timezone_abbreviation: str
    elevation: int
    daily_units: DailyUnits
    daily: Daily


class TemperatureResponse(BaseModel):
    low: float
    high: float


class TimeZone(BaseModel):
    model_config = ConfigDict(extra="allow")
    timeZoneId: str


class GeoName(BaseModel):
    model_config = ConfigDict(extra="allow")
    timezone: TimeZone
    lat: float
    lng: float


class CoordsResponse(BaseModel):
    model_config = ConfigDict(extra="allow")
    geonames: List[GeoName]


class WeatherPlugin(BasePlugin):
    @staticmethod
    def _get_temp_url_for_location(lat: float, lng: float, timezone: str) -> str:
        return f"https://api.open-meteo.com/v1/forecast?latitude={str(lat)}&longitude={str(lng)}&daily=temperature_2m_max,temperature_2m_min&temperature_unit=fahrenheit&wind_speed_unit=mph&precipitation_unit=inch&timezone={timezone}&forecast_days=1"

    @staticmethod
    def call_openai(location):
        system_prompt = "respond with a json formatted response"
        system_prompt = (
            system_prompt + "that contains the longitude, latitude, and timezone"
        )
        system_prompt = (
            system_prompt + "for the location. timezoneid should be formatted like EST"
        )
        prompt = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": location},
        ]
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-2024-08-06",
            messages=prompt,
            temperature=0,
        )
        return completion.choices[0].message.content

    @staticmethod
    def _get_loc_url_for_location(self, location_string: str) -> str:
        results = WeatherPlugin.call_openai(location_string)
        return results

    @kernel_function(
        description="Retrieve low and high temperatures for the day for a given location"
    )
    def get_temperature(
        self, lat: float, lng: float, timezone: str
    ) -> TemperatureResponse:
        url = WeatherPlugin._get_temp_url_for_location(lat, lng, timezone)

        response = requests.get(url).json()
        if response:
            response_int: TemperatureResponseInt = TemperatureResponseInt(**response)
            return TemperatureResponse(
                low=response_int.daily.temperature_2m_min[0],
                high=response_int.daily.temperature_2m_max[0],
            )
        else:
            raise ValueError("Error retrieving temperature")

    @kernel_function(
        description="Retrieve the latitude, longitude, and timezone for a given location search string"
    )
    def get_lat_lng_for_location(self, location_string: str) -> LocationCoordinates:
        results = WeatherPlugin._get_loc_url_for_location(self, location_string)

        if results:
            return results
        else:
            raise ValueError("Error retrieving location coordinates")
