import os
import re
import sys
import json
import unittest
import asyncio
from unittest.mock import patch, mock_open, MagicMock

# --- Begin: Setup logger module mocking with print statements ---
class PrintLogger:
    def info(self, message):
        print("INFO:", message)

    def debug(self, message):
        print("DEBUG:", message)

    def error(self, message):
        print("ERROR:", message)

# Insert mock modules into sys.modules so that the import in custom_plugins.py won't fail.
sys.modules['logger'] = MagicMock()
sys.modules['logger.agent_loggers'] = MagicMock()
sys.modules['logger.agent_loggers'].Logger = PrintLogger
# --- End: Setup logger module mocking with print statements ---

# Ensure our parent directory is in sys.path for module imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, ".."))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from custom_plugins import DepartmentAssignmentPlugin

class TestDepartmentAssignmentPlugin(unittest.TestCase):
    """
    Unit tests for the DepartmentAssignmentPlugin class.
    
    This test suite verifies the functionality of the assign_department method
    in the DepartmentAssignmentPlugin class. It ensures that the method correctly
    assigns the department based on the provided data.
    """
    
    def test_assign_department(self):
        """
        Test that the assign_department method correctly assigns the department.
        
        This test verifies that when provided with recommended changes data,
        the assign_department method correctly assigns the Correct Team to the
        "assigned_department" field.
        """
        # Sample data structure
        sample_recommended_changes = [{
            '_CASE_KEY': '1109330510946374720240010208',
            '_INVOICE_KEY': '110933051094637472024001',
            'VENDOR_CODE': '0003097722',
            'PAYMENT_METHOD': 'M',
            'RISK_TEXT': 'Payment Method on the Invoice and Vendor Master Data does not match.',
            'AGENT_IDENTIFIED_RISK': 'The payment method has changed, indicating a potential risk of payment processing discrepancies.',
            'AGENT_COMMNET': 'Invoice 35765 is impacted due to a change in payment method. The previous payment method was not provided, and the new payment method is M. This change may lead to discrepancies in payment processing.',
            'AGENT_RECOMMENDED_CHANGE': 'Verify the payment method change with the vendor to ensure it is intentional and authorized. Update the Vendor Master Data to reflect the correct payment method if the change is confirmed. Ensure that all future invoices align with the updated payment method to prevent discrepancies. Document the verification process and any changes made for audit purposes.',
            'assigned_department': 'Finance Team'
        }]
        
        mock_kernel = MagicMock()
        plugin = DepartmentAssignmentPlugin(mock_kernel)
        result = plugin.assign_department(sample_recommended_changes)

        # Check if the result is of type string
        if isinstance(result, str):
            match = re.search(r'"assigned_department"\s*:\s*"([^"]+)"', result)
            if match:
                assigned_department = match.group(1)
                
            # Assertions
            assert assigned_department is not None, "assigned_department value is null"
            assert assigned_department == "Finance Team", "assigned_department value is not 'Finance Team'"

        # Check if the result is a list of dictionaries
        elif isinstance(result, list) and all(isinstance(item, dict) for item in result):
            for item in result:
                assigned_department = item.get("assigned_department")
                
                # Assertions
                assert assigned_department is not None, "assigned_department value is null"
                assert assigned_department == "Finance Team", "assigned_department value is not 'Finance Team'"

        else:
            raise TypeError("The result is neither a string nor a list of dictionaries")

if __name__ == '__main__':
    unittest.main(verbosity=2)