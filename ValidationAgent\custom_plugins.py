import asyncio
import yaml
import logging
from semantic_kernel.functions import kernel_function

logger = logging.getLogger(__name__)


def load_config(file_path: str):
    with open(file_path, "r") as file:
        return yaml.safe_load(file)


# Load the configuration
config = load_config("agents/plugin-configs.yaml")


class Checklist:
    @kernel_function(description="This function gets the checklist")
    async def get_check_list(self, document_type: str, section_name: str) -> str:
        return config[document_type][section_name]["checklist"]
