# Vulnerability Agent

This agent wraps the Microsoft api: https://api.msrc.microsoft.com/cvrf/v3.0/cvrf/
which can be used to get monthly reports of the vulnerabilities found. The agent can
be queries to retreive one month or multimonth reports

## Environment Variables

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=VulnerabilityAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_MICROSOFT_API=https://api.msrc.microsoft.com/cvrf/v3.0/cvrf/
```

## Environment Variables for platform deployment

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=VulnerabilityAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_MICROSOFT_API=https://api.msrc.microsoft.com/cvrf/v3.0/cvrf/
```

## Dedicated Hardware

- no dedicated hardware needed