import json
import pandas as pd
import boto3
import os
from io import <PERSON><PERSON>
from dotenv import load_dotenv
from logger.agent_loggers import Logger

# Initialize a logger for AWS connector operations.
aws_connector_logger = Logger()

# Load environment variables
load_dotenv()

# Retrieve S3 credentials from environment variables
S3_ACCESS_KEY = os.environ.get("S3_ACCESS_KEY")
S3_SECRET_KEY = os.environ.get("S3_SECRET_KEY")
S3_BUCKET = os.environ.get("S3_BUCKET")
S3_EMAILDATA_FILE = os.environ.get("S3_EMAILDATA_FILE")
S3_ATTACHPATHFILE = os.environ.get("S3_ATTACHPATHFILE")
S3_PRODUCT_DETAILS = os.environ.get("S3_PRODUCT_DETAILS")

def read_s3_file(bucket, s3_file):
    """Helper function to read a file from S3 and return as a DataFrame"""
    aws_connector_logger.info(f"Attempting to read file from S3: Bucket={bucket}, S3 File ={s3_file}")
    
    # Create an S3 client
    s3 = boto3.client('s3', aws_access_key_id=S3_ACCESS_KEY, aws_secret_access_key=S3_SECRET_KEY)
    
    try:
        # Get the object from S3
        response = s3.get_object(Bucket=bucket, Key=s3_file)
        aws_connector_logger.info(f"Successfully retrieved file: {s3_file}")
        
        # Read the content of the file
        content = response['Body'].read()
        
        # Determine file type and read accordingly
        if s3_file.endswith('.csv'):
            # Use StringIO to read CSV data
            data = StringIO(content.decode('utf-8'))
            df = pd.read_csv(data, delimiter=',', quotechar='"', skipinitialspace=True, on_bad_lines='skip')
            aws_connector_logger.info(f"Successfully read CSV data from {s3_file}")
        elif s3_file.endswith('.xls') or s3_file.endswith('.xlsx'):
            # Use pd.read_excel for Excel files
            df = pd.read_excel(content)
            aws_connector_logger.info(f"Successfully read Excel data from {s3_file}")
        else:
            raise ValueError("Unsupported file type")
        
        return df
    except Exception as e:
        aws_connector_logger.error(f"Error reading file from S3: {e}")
        raise


def readS3():
    """
    Reads and processes email and attachment data from S3, then saves and returns the result as a JSON string.

    Returns:
        str: The combined result as a JSON string.
    """
    aws_connector_logger.info("Starting to read and process email and attachment data from S3")

    try:
        # Read email data file
        email_df = read_s3_file(S3_BUCKET, S3_EMAILDATA_FILE)
        aws_connector_logger.info("Email data file read successfully")

        # Read attachment path file
        attach_df = read_s3_file(S3_BUCKET, S3_ATTACHPATHFILE)
        aws_connector_logger.info("Attachment path file read successfully")

        # Process email data
        email_data = []
        for index, row in email_df.iterrows():
            email_data.append({
                "key": row['Key'],
                "Key_confidence_score": float(row['Key Confidence']),
                "value": row['Value'],
                "Value_confidence_score": float(row['Value Confidence'])
            })
        aws_connector_logger.info("Processed email data successfully")

        # Process attachment data
        attachment_data = []
        for index, row in attach_df.iterrows():
            attachment_item = {}
            for column in attach_df.columns:
                # Handle numeric values appropriately
                if pd.api.types.is_numeric_dtype(attach_df[column]):
                    attachment_item[column] = float(row[column])
                else:
                    attachment_item[column] = row[column]
            attachment_data.append(attachment_item)
        aws_connector_logger.info("Processed attachment data successfully")

        # Extract the email ID from the S3 email data file path
        email_id = S3_EMAILDATA_FILE.split('/')[0]
        aws_connector_logger.info(f"Extracted email ID: {email_id}")

        # Combine results
        result = {
            "email_id": email_id,
            "email_data": email_data,
            "attachment_data": attachment_data
        }

        # Convert the result dictionary to a JSON string with indentation
        result_json = json.dumps(result, indent=4)
        aws_connector_logger.info("Successfully converted results to JSON format")

        # Return the JSON string
        return result_json
    except Exception as e:
        aws_connector_logger.error(f"Error processing S3 data: {e}")
        raise

def readProductDetailsFromS3():
    product_details_df = read_s3_file(S3_BUCKET,S3_PRODUCT_DETAILS)
    return product_details_df