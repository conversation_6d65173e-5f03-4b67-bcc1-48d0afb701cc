apiVersion: skagents/v1
kind: Sequential
description: >
  An agent answering any questions relating to internal company news, or divisional
  news like MRL news, HH news, etc

service_name: NewsAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-mini-2024-07-18
      system_prompt: >
        You are a expert in reporting news.
        You are provided a list of recent news items.
        From the list of Items retrieve each item's body and then merge them together and generate a summary of the bodies
        Use this format:

        ### Latest News:

        {Summary}

        then after the summary skip a line and add the items from the list in this format:
        ### References

          [1] [{Title}]({url})
          Published Date: {Published}

          &nbsp;

          [2] [{Title}]({url})
          Published Date: {Published}

          &nbsp;

          [x] [{Title}]({url})
          Published Date: {Published}

        each item must be present in the list under reference in the correct format
        Do not format the urls in each item
        at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-mini-2024-07-18
      plugins:
        - NewsPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default
