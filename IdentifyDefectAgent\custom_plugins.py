import os
import json
import yaml
from dotenv import load_dotenv
from typing import Any, Dict, Optional
from semantic_kernel import <PERSON><PERSON>
from semantic_kernel.functions import kernel_function
from semantic_kernel.connectors.ai.open_ai import OpenAIPromptExecutionSettings
from logger.agent_loggers import Logger
from sk_agents.ska_types import BasePlugin
from openai import AzureOpenAI
import re

# Load environment variables from .env file
load_dotenv()

# Initialize logger
logger = Logger()

def load_rules(file_path: str) -> Dict:
    """Load rules from a YAML file."""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            rules = yaml.safe_load(file)
            logger.debug(f"Loaded rules: {rules}")
            return rules
    except FileNotFoundError:
        logger.error(f"Rules file not found at {file_path}. No rules loaded.")
        return {}
    except Exception as e:
        logger.error(f"Error loading rules from {file_path}: {e}. No rules loaded.")
        return {}

class IdentifyDefectPlugin(BasePlugin):
    """Plugin to identify defects in Celonis data using LLM and YAML rules."""

    def __init__(self, kernel: Kernel, authorization: Optional[Dict[str, Any]] = None, 
                 extra_data_collector: Optional[Any] = None):
        self.kernel = kernel
        self.sample_dir = self._find_sample_dir()
        self.rules_file = os.path.join(self.sample_dir, 'defect_rules.yaml')
        self.rules = load_rules(self.rules_file)


    def _find_sample_dir(self) -> str:
        """Find the sample directory using multiple possible paths."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths = [
            os.path.join(os.path.dirname(current_dir), "sample"),  
            os.path.join(current_dir, "sample"),                   
            os.path.join(os.path.dirname(os.path.dirname(current_dir)), "sample"), 
        ]
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found sample directory at: {path}")
                return path
        return ""

    @staticmethod
    def call_openai(prompt: str) -> str:
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        try:
            completion = client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
            )
            logger.debug("OpenAI API call successful.")
            return completion.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return "Error during API call."
        

    @kernel_function(
        name="identify_defects",
        description="Identify defects in Celonis data based on rules"
    )
    async def identify_defects(self, celonis_data) -> str:
        """
        Identifies risks and defects in process data using LLM.

        Args:
            celonis_data : The input data from Celonis.

        Returns:
            str: JSON with only identified defects using classification rules.
        """
        
        prompt = f"""
You are an expert in identifying process risks from Celonis data.

Defect Detection Rules:
{json.dumps(self.rules, indent=2)}

Celonis Input Data:
{json.dumps(celonis_data, indent=2)}

Your Task:
1. Check each record against all `defect_rules`.
2. Select only the record which violates the rules. Skip if that says no issues.
3. If a record matches any rule, classify it by returning only the fields in `celonis_data_classification_rules`.
4. For the `comment` field, briefly describe the nature of the defect in plain language. **Do not include the rule ID.
5. Do not explain. Return the updated list as a valid JSON array.
"""
        
        detected_defects_json = self.call_openai(prompt=prompt)
        
        return detected_defects_json
