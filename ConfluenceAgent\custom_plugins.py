import os
import concurrent.futures
from requests.auth import HTTP<PERSON>asic<PERSON><PERSON>
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin
from openai import AzureOpenAI
import html2text
import httpx
import asyncio


class ConfluencePlugin(BasePlugin):
    web_ui_url = os.environ.get("TA_UI_CONFLUENCE_URL")
    api_base_url = os.environ.get("TA_BASE_CONFLUENCE_URL")
    username = os.environ.get("TA_USERNAME")
    password = os.environ.get("TA_PASSWORD")
    api_key = os.environ.get("TA_CONFLUENCE_API_KEY")
    api_key_name = os.environ.get("TA_API_GATEWAY_KEY_NAME")

    async def call_confluence_api(self, text, space=None, limit=3):
        url = f"{self.api_base_url}content/search?cql=title~'{text}'+or+text~'{text}'&expand=body.view,space&limit={limit}"
        if space:
            url = f"{self.api_base_url}content/search?cql=space.key={space}+and+(title~'{text}'+or+text~'{text}')&expand=body.view,space&limit={limit}"
        headers = {self.api_key_name: self.api_key}
        async with httpx.AsyncClient() as client:
            response = await client.get(
                url,
                headers=headers,
                auth=HTTPBasicAuth(
                    self.username,
                    self.password
                )
            )
            response.raise_for_status()
            data = response.json()
            return data

    def call_openai(self, prompt):
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        completion = client.beta.chat.completions.parse(
            model="gpt-4o-mini-2024-07-18",
            messages=prompt,
            temperature=0,
        )
        return completion.choices[0].message.content

    def generate_summary(self, prompt):
        if prompt == []:
            return "Not available currently"
        else:
            summary = self.call_openai(prompt)
            return summary

    async def generate_prompt(self, prompt_type, body_content, text=None):
        prompt = []
        if prompt_type == "summary" and body_content != "":
            prompt = [
                {
                    "role": "system",
                    "content": "generate a 3 sentence summary of the content",
                },
                {"role": "user", "content": body_content},
            ]
        elif prompt_type == "process" and body_content != "":
            prompt = [
                {
                    "role": "system",
                    "content": f"generate a step by step process to answer: {text} using the user content",
                },
                {"role": "user", "content": body_content},
            ]
        return prompt

    async def parse_confluence_data(self, data, prompt_type, text, limit=5):
        parsed_results = []
        url = ""
        search_results = data.get("results", [])

        # Limit processing to 5 items
        for index, item in enumerate(search_results):
            if index >= limit:
                break
            links = item.get("_links")
            if links:
                web_ui_endpoint = links.get("webui")
                if web_ui_endpoint:
                    url = f"{self.web_ui_url}{web_ui_endpoint}"

            # Prepare a tuple with the body for summary generation
            body_value = item["body"]["view"]["value"]
            config = html2text.HTML2Text()
            config.body_width = 0  # disable line wrapping
            text_content = config.handle(body_value)
            # Store item details for later use
            prompt = await self.generate_prompt(
                prompt_type,
                text_content,
                text
            )
            new_item = {
                "Type": item["type"],
                "Title": item["title"],
                "Space Key": item["space"]["key"],
                "Space Name": item["space"]["name"],
                "Url": url,  # URL is still needed here
                "body_value": text_content,  # Store for summary generation
                "prompt": prompt,
            }
            parsed_results.append(new_item)

        # Use ThreadPoolExecutor to generate summaries in a separate thread
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # Map the generate_summary function to the body values
            summaries = list(
                executor.map(
                    self.generate_summary,
                    [item["prompt"] for item in parsed_results]
                )
            )

        # Add summaries back to the new_item
        for item, summary in zip(parsed_results, summaries):
            item["Summary"] = summary
            item["body_value"] = None
            item["prompt"] = None
        return parsed_results

    @kernel_function(
        description="when a users asks for a search in confluence or general query use this function"
    )
    async def get_confluence(self, text: str):
        try:
            data = await self.call_confluence_api(text)
            parsed_data = await self.parse_confluence_data(
                data,
                "summary",
                text
            )
            if parsed_data:
                return parsed_data
            else:
                return "no results found"
        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    @kernel_function(
        description="when a users asks for a search or general query in specific space in confluence"
    )
    async def get_confluence_limited_space(self, text: str, space: str):
        try:
            data = await self.call_confluence_api(text, space)
            parsed_data = await self.parse_confluence_data(data)
            if parsed_data:
                return parsed_data
            else:
                return "no results found"
        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    @kernel_function(
        description="when a users asks for a detailed process or step by step guide use this function"
    )
    async def get_process_confluence(self, text: str):
        try:
            data = await self.call_confluence_api(text, limit=1)
            parsed_data = await self.parse_confluence_data(
                data,
                "process",
                text,
                1
            )
            if parsed_data:
                return parsed_data
            else:
                return "no results found"

        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    @kernel_function(
        description="when a users asks for a detailed process or step by step guide in a specific space use this function"
    )
    async def get_process_confluence_limited_space(
        self,
        text: str,
        space: str
    ):
        try:
            data = await self.call_confluence_api(
                text,
                space,
                limit=1
            )
            parsed_data = await self.parse_confluence_data(
                data,
                "process",
                text,
                1
            )
            if parsed_data:
                return parsed_data
            else:
                return "no results found"

        except Exception as error:
            print(f"error encounter: {error}")
            raise error
