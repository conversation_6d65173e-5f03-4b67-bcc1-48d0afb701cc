import logging
import inspect
import os
import datetime
from typing import Dict
import yaml

ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
SAMPLE_FOLDER = os.path.join(ROOT_DIR, "sample")  # Log directory
CONFIG_FILE_PATH = os.path.join(ROOT_DIR, "config", "config.yaml")

# Load configuration from a YAML file
def load_config(config_path: str) -> Dict:

    try:
        with open(config_path, "r") as file:
            return yaml.safe_load(file)
        
    except FileNotFoundError:        
        return {}
    
    except Exception as e:        
        return {}
    
CONFIG = load_config(CONFIG_FILE_PATH)


# Ensure the sample folder exists
if not os.path.exists(SAMPLE_FOLDER):
    os.makedirs(SAMPLE_FOLDER)  # Create the folder if it doesn't exist

# Generate a dynamic filename with a timestamp
LOG_FILE_NAME = f"agent_log_{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.txt"
LOG_FILE_PATH = os.path.join(SAMPLE_FOLDER, LOG_FILE_NAME)

class Logger:
    _instance = None  

    def __new__(cls, log_file=LOG_FILE_PATH):
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._initialize_logger(log_file)
        return cls._instance
    
    def _initialize_logger(self, log_file):
        self.logger = logging.getLogger("CustomLogger")

        if not self.logger.handlers:  # Prevent duplicate handlers
            self.logger.setLevel(logging.DEBUG)
            file_handler = logging.FileHandler(log_file, mode='a')
            file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
            self.logger.addHandler(file_handler)

            logging.getLogger("openai").setLevel(logging.WARNING)
            logging.getLogger("semantic_kernel").setLevel(logging.WARNING)

        # Check and maintain only 10 log files
        self._manage_log_files()

    def _manage_log_files(self):
        """Keeps only the 10 most recent log files in the sample folder."""
        log_files = sorted(
            [os.path.join(SAMPLE_FOLDER, f) for f in os.listdir(SAMPLE_FOLDER) if f.startswith("agent_log_")],
            key=os.path.getctime  # Sort files by creation time (oldest first)
        )

        if len(log_files) > 10:
            oldest_file = log_files[0]  # Get the oldest file
            os.remove(oldest_file)  # Delete the oldest file
            self.logger.info(f"Deleted oldest log file: {oldest_file}")

    def _get_caller_info(self):
        frame = inspect.currentframe().f_back.f_back
        class_name = frame.f_locals.get('self', None).__class__.__name__ if 'self' in frame.f_locals else 'Standalone'
        function_name = frame.f_code.co_name
        return f"{class_name}.{function_name}"

    def debug(self, message):
        self.logger.debug(f"{self._get_caller_info()} - {message}")

    def info(self, message):
        self.logger.info(f"{self._get_caller_info()} - {message}")

    def error(self, message):
        self.logger.error(f"{self._get_caller_info()} - {message}")