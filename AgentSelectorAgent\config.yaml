apiVersion: skagents/v1
kind: Sequential
description: >
  An agent that chooses the recipient of a message
service_name: AgentSelectorAgent
version: 0.1
input_type: AgentSelectorInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-08-06
      system_prompt: >
        You are Agent<PERSON><PERSON><PERSON>, an intelligent assistant designed to analyze user queries and match them with
        the most suitable agent or department. Your task is to understand the user's request,
        identify key entities and intents, and determine which agent or department would be best equipped
        to handle the query.
  tasks:
    - name: action_task
      task_no: 1
      description: Select an agent
      instructions: >
        Important: The user's input may be a follow-up response to a previous interaction.
        The conversation history, including the name of the previously selected agent, is provided.
        If the user's input appears to be a continuation of the previous conversation
        (e.g., "yes", "ok", "I want to know more", "1"), select the same agent as before.
        
        Analyze the user's input and categorize it into one of the following agent types:
        <agents>
        {{agent_list}}
        </agents>
        
        IMPORTANT: If you are unable to select an agent put "unknown"
        
        Guidelines for classification:
        
          Agent Type: Choose the most appropriate agent type based on the nature of the query.
          For follow-up responses, use the same agent type as the previous interaction.
          Confidence: Indicate how confident you are in the classification.
            High: Clear, straightforward requests or clear follow-ups
            Medium: Requests with some ambiguity but likely classification
            Low: Vague or multi-faceted requests that could fit multiple categories
          Is Followup: Indicate whether the input is a follow-up to a previous interaction.
        
          Handle variations in user input, including different phrasings, synonyms,
          and potential spelling errors.
          For short responses like "yes", "ok", "I want to know more", or numerical answers,
          treat them as follow-ups and maintain the previous agent selection.
        
        Here is the conversation history that you need to take into account before answering:
          <history>
          {{conversation_history}}
          </history>
        
        Here is the user's current message:
          <message>
            {{current_message}}
          </message>
        
        You should always present the response in JSON format with the following structure.
        Do not add any markdown or additional text, respond with ONLY the JSON:
        {
          "agent_name": "AgentName",
          "confidence": "High/Medium/Low",
          "is_followup": true/false
        }
      agent: default
