import json
import yaml
import asyncio
import traceback
from typing import List, Di<PERSON>, <PERSON><PERSON>, Annotated
from semantic_kernel import <PERSON><PERSON>
from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion
from semantic_kernel.functions.function_result import FunctionResult
from semantic_kernel.functions.kernel_arguments import KernelArguments
from semantic_kernel.functions import kernel_function
# Import the specific execution settings class for OpenAI
from semantic_kernel.connectors.ai.open_ai import OpenAIPromptExecutionSettings

# ... (Keep helper functions load_classification_rules and extract_email_data) ...
# Helper Function: Load YAML classification rules
def load_classification_rules(file_path: str) -> List[str]:
    try:
        with open(file_path, "r") as file:
            data = yaml.safe_load(file)
        # Ensure rules are loaded as a list of strings and clean whitespace
        rules = data.get("order_classification_rules", [])
        if isinstance(rules, str): # Handle if loaded as a single comma-separated string
            rules = [rule.strip() for rule in rules.split(',') if rule.strip()]
        elif isinstance(rules, list):
            rules = [str(rule).strip() for rule in rules if str(rule).strip()]
        else:
            rules = []
        return rules
    except FileNotFoundError:
        print(f"Warning: Rules file not found at {file_path}. No rules loaded.")
        return []
    except Exception as e:
        print(f"Warning: Error loading rules from {file_path}: {e}. No rules loaded.")
        return []


# Helper Function: Extract email data
def extract_email_data(email_data: dict) -> Tuple[str, str, List[str]]:
    subject = ""
    body_parts = [] # Collect body parts separately
    attachments = []
    subject_found = False

    for block in email_data.get('Blocks', []):
        text = block.get("Text", "").strip()
        if not text: # Skip empty blocks
             continue

        text_lower = text.lower()

        # More robust subject/body detection
        if not subject_found and text_lower.startswith("subject:"):
            subject = text[len("Subject:"):].strip()
            subject_found = True
        elif text_lower.startswith("body:"):
            # Add the part after "Body:", potentially multiline start
            body_parts.append(text[len("Body:"):].strip())
        else:
            # If subject is found, assume remaining lines are body unless identified otherwise
            if subject_found:
                 body_parts.append(text)
            # If subject not found yet, treat as part of body (or potentially subject if it matches pattern later?)
            # This simplistic approach might misclassify if "Subject:" appears later.
            # A more robust parser might be needed for complex emails.
            else:
                 # Check if this line looks like a subject even without the prefix
                 # Very basic heuristic - could be improved
                 if not subject_found and len(body_parts) == 0 and len(text) < 100 and ':' not in text[:len(text)//2]:
                     subject = text
                     subject_found = True
                 else:
                     body_parts.append(text)


    body = " ".join(body_parts).strip() # Join all collected body parts

    for attachment in email_data.get('Attachments', []):
        attachment_texts = []
        for block in attachment.get("Blocks", []):
            text = block.get("Text", "")
            # Handle potential nested structures like tables more explicitly if needed
            if block.get("BlockType") == "TABLE":
                 # Basic table text extraction (can be improved)
                 table_text = ""
                 for row in block.get("Rows", []):
                     cell_texts = [cell.get("Text", "") for cell in row.get("Cells", [])]
                     table_text += " | ".join(cell_texts) + "\n"
                 attachment_texts.append(f"[Table Data:\n{table_text.strip()}\n]")
            elif text: # Only append non-empty text
                 attachment_texts.append(text)
        if attachment_texts: # Only add if there was content
             attachments.append(" ".join(filter(None, attachment_texts))) # Join texts for one attachment

    # If subject still empty after parsing, assign a default
    if not subject and body:
        subject = body[:50] + "..." # Use start of body as fallback subject
        print("Warning: No 'Subject:' line found, using start of body as subject.")


    return subject, body, attachments


class EmailClassificationPlugin:
    def __init__(self, kernel: Kernel, rules_path: str):
        self.kernel = kernel
        self.rules = load_classification_rules(rules_path)
        print(f"Loaded rules: {self.rules}") # Debug print for loaded rules

    @kernel_function(
        name="classify_email",
        description="Determines if an email is an 'order' or not based on its content. Compare the email content with the classification rules and return 'order' or 'not order'."
    )
    async def classify_email(
        self,
        subject: Annotated[str, "The subject of the email."],
        body: Annotated[str, "The body of the email."],
        attachments: Annotated[List[str], "List of attachment texts."]
    ) -> str:
        # Prepare content, ensuring attachments are handled even if empty
        attachment_content = ' | '.join(filter(None, attachments)) # Filter out empty strings
        # Clean up the rules display - remove the single quotes and extra spaces from previous output
        rules_string = ", ".join(self.rules)

        full_content = f"Subject: {subject}\nBody: {body}\nAttachments: {attachment_content if attachment_content else 'None'}"

        prompt = (
            f"You are an email classification agent.\n"
            f"Analyze the following email content and classify it strictly as either 'order' or 'not order'.\n"
            # Use the cleaned rules_string here
            f"Consider these keywords/phrases often associated with orders: {rules_string}\n\n"
            f"Email Content:\n---\n{full_content}\n---\n\n"
            f"Based ONLY on the content and the keywords, is this an order-related email? Respond ONLY with the word 'order' or the word 'not order'."
        )

        try:
            # ---- Explicitly define execution settings for the target service ---
            execution_settings = OpenAIPromptExecutionSettings(
                service_id="openai",  # Explicitly target the service added in main()
                ai_model_id="gpt-4", # Can optionally specify model again, ensures consistency
                max_tokens=10,
                temperature=0.0,
                top_p=1.0 # Often good to set top_p with temperature 0
                # Add other relevant OpenAI settings if needed
            )
            # --------------------------------------------------------------------

            print(f"\n--- Sending Prompt to LLM (using invoke_prompt with service_id='openai') ---\n{prompt[:500]}...\n---------------------------\n") # Debug print

            # Pass the execution settings object via KernelArguments
            # The key for settings might just be 'settings' or specific like 'ai_settings' depending on context/version
            # Often, passing it directly to invoke is cleaner if the method supports it.
            # Let's try passing it directly first:
            result: FunctionResult = await self.kernel.invoke_prompt(
                prompt=prompt,
                arguments=KernelArguments(settings=execution_settings) # Pass settings object here
                # Alternatively, if invoke_prompt takes settings directly:
                # arguments=KernelArguments(), # Empty if no other args needed by template
                # settings=execution_settings
            )

            response_text = str(result).strip().lower()
            print(f"--- LLM Raw Response ---\n{response_text}\n------------------------\n")

            # Validate the response
            if response_text == "order":
                return "order"
            elif response_text == "not order":
                 return "not order"
            else:
                 print(f"Warning: LLM returned an unexpected response: '{response_text}'. Defaulting to 'not order'.")
                 return "not order"

        except Exception as e:
            print(f"Error during kernel prompt invocation: {e}")
            traceback.print_exc()
            return "error"

# ... (Keep the main function exactly as it was in the previous step) ...
async def main():
    EMAIL_FILE = "" # Use raw string for paths
    RULES_FILE = "" # Use raw string for paths
    API_KEY = "YOUR_OPENAI_API_KEY"  # Replace with your key

    if API_KEY == "YOUR_OPENAI_API_KEY":
        print("Error: Please replace 'YOUR_OPENAI_API_KEY' with your actual OpenAI API key.")
        return

    kernel = Kernel()

    try:
        kernel.add_service(OpenAIChatCompletion(
            service_id="openai", # Service ID used in execution settings
            api_key=API_KEY,
            ai_model_id="gpt-4"
        ))
        print("OpenAI Service added successfully.") # Confirmation
    except Exception as e:
        print(f"Error initializing Kernel or adding OpenAI service: {e}")
        return

    email_plugin = EmailClassificationPlugin(kernel, RULES_FILE)
    kernel.add_plugin(email_plugin, plugin_name="email_classification_plugin")

    try:
        with open(EMAIL_FILE, "r", encoding='utf-8') as f:
            email_data = json.load(f)["emails"]
    except FileNotFoundError:
        print(f"Error: Email data file not found at {EMAIL_FILE}")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {EMAIL_FILE}")
        return
    except Exception as e:
        print(f"Error loading email data: {e}")
        return

    for i, email in enumerate(email_data):
        email_id = email.get("email_id", f"unknown_id_{i+1}")
        print("-" * 30)
        print(f"Processing Email ID: {email_id}")
        try:
            subject, body, attachments = extract_email_data(email)
            print(f"Extracted Subject: '{subject}'")
            # Limit body print length further if needed
            print(f"Extracted Body (first 80 chars): '{body[:80]}...'")
            print(f"Extracted Attachments Count: {len(attachments)}")

            print(f"Classifying email...")

            # Create KernelArguments instance
            invoke_args = KernelArguments(
                subject=subject,
                body=body,
                attachments=attachments
            )

            # Use the kernel function to classify the email
            result: FunctionResult = await kernel.invoke(
                plugin_name="email_classification_plugin",
                function_name="classify_email",
                arguments=invoke_args # Pass the KernelArguments object
            )

            classification = str(result).strip().lower()

            print(f"Email ID: {email_id}, Classification: {classification}")
            if classification != "order":
                # Check if it was an error or genuinely 'not order'
                if classification == 'error':
                     print("→ Error occurred during classification.")
                else:
                     print("→ Manual review likely needed (classified as not order).")

        except Exception as e:
            print(f"Error processing email ID {email_id}: {e}")
            traceback.print_exc() # Ensure traceback is printed for errors here too
        print("-" * 30 + "\n")


if __name__ == "__main__":
    asyncio.run(main())