apiVersion: skagents/v1
kind: Sequential
description: >
  A weather chat agent
service_name: WeatherBot
version: 0.1
input_type: BaseInputWithUserContext
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o
      system_prompt: >
        You are a helpful assistant.
      plugins:
      - WeatherPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
        
        The following user context was provided:
          User Location: {{user_context["User Location"]}}

#        --If you wanted to do all user context, you could do something like this--
#        The following user context was provided:
#          {% for key, value in user_context.items() %}
#            {{key}}: {{value}}
#          {% endfor %}
      agent: default
