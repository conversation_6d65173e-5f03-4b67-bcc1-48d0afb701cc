# Confidence Score Agent README

## 1. Overview
The ConfidenceScoreAgent takes the extrcated mail data for mails which are classified as Order by the EmailClassifying Agent and than map the data with mapping.yaml to get relevant fields and than calculate the average confidence score of mapped data.

## 2. Features
- Takes the extracted email data from EmailClassifyingAgent.
- Map the data and calculate average confidence score.
- Returns results in a structured JSON format.
- Logs mapping and confidence score calculating activities for monitoring and debugging.

## 3. Dependencies
This agent depends on the following files:
- **Main Agent File**: `gbsdsai-agent-configs/agent2_uc1.py`
- **Configuration File**: `ConfidenceScoreAgent/config.yaml`
- **Rules File**: `../sample/mapping.yaml`

### Environment Variables
A `.env` file needs to be created at `ConfidenceScoreAgent/.env` with the following required values:

TA_API_KEY="your API key"
TA_SERVICE_CONFIG=ConfidenceScoreAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06


## 4 How the Agent Works

Configuration Loading:
The agent loads its configuration from ConfidenceScoreAgent/config.yaml, defining its structure, input types, and tasks.
Plugin Initialization:
The ConfidenceScorePlugin class is initialized from custom_plugins.py, setting up logging and loading mapping rules from mapping.yaml.
Email Processing:
Task 1: Map Email Fields:
Takes EmailClassifyingAgent Ouput as an input.
Loads mapping rules from mapping.yaml.
Applies mapping rules to extract fields .

Confidence Score Calculation:
Task 2: Calculate Overall Confidence Scores:
Calculates average confidence scores for each email.
Determines if manual review is needed (score < 85%).
Returns a JSON with email ID, average confidence score, review status, and mapped fields.

Notification Status Processing:
Task 3: Process Notification Status:
Adds notification_sent field based on manual_review_needed status.
Returns the processed JSON with the added field.

Logging:
Logs actions and events using the Logger class.
Creates a log file in the /sample/ directory for each run, named with the format DDMMYY+time.

## 5. Usage
To run the agent, follow these steps:
i)Navigate to the main folder.
ii)Activate the virtual environment using the following command:
.venv/scripts/activate
iii) Run the agent with the command:

python gbsdsai-agent-configs/agent2_uc1.py

## 6 Sample Output 
	
[
  {
    "email_id": 1,
    "average_confidence_score": 92.23,
    "manual_review_needed": true,
    "fields": [
      {
        "field": "from",
        "value": {
          "name": "Garzon Munoz, Kevin Steven",
          "email": "<EMAIL>"
        },
        "confidence_score": 95.35
      },
      {
        "field": "order_details",
        "value": {
          "order_number": "13373397",
          "order_date": "2025-05-02",
          "delivery_date": "2025-05-09",
          "client": "CRUZ VERDE_INST",
          "destination": "COTA INSTITUCIONAL - CEDI ORION LOTE SAN GREGORIO COTA",
          "delivery_address": "Centro de Distribución Orion - Parque Logístico Constellation, Vereda Vuelta Grande Km 1 vía potrero chico 1 COLOMBIA-COTA",
          "payer_code": "3000009063",
          "sold_to_code": "50084863",
          "ship_to_code": "50084863",
          "total_value": "293,018,432"
        },
        "confidence_score": 88.86
      }
    ],
    "notification_sent": false
  }
]


## 7 Testing

The agent includes a suite of unit tests located at ConfidenceScoreAgent/tests/test_custom_plugins.py. These tests verify the functionality of the ConfidenceScorePlugin class. 

The primary tests included are:

Mapping Rules Retrieval:
Test: test_get_mapping_rules_returns_correct_mapping
Purpose: Validates that the get_mapping_rules method correctly converts a YAML mapping file into the expected JSON format.
Notification Status Processing:

Test: test_process_notification_status_with_manual_review_true
Purpose: Confirms that when manual_review_needed is set to True, the notification_sent flag is set to False.

Test: test_process_notification_status_with_manual_review_false
Purpose: Ensures that when manual_review_needed is False, the notification_sent flag is set to True.

To run the tests, follow these steps:

Navigate to the ConfidenceScoreAgent folder.
Execute the following command in the terminal:
python -m unittest -v .\tests\test_custom_plugins.py

## 8 API Access
Once running, access the Swagger documentation at:
http://localhost:6010/ConfidenecScoreAgent/0.1/docs