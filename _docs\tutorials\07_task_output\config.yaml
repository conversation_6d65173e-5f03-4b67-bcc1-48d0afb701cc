apiVersion: skagents/v1
kind: Sequential
description: >
  Add numbers 1 & 2, then multiply the result by number 3 and add 10.
service_name: MathAgent
version: 0.1
input_type: NumbersInput
output_type: MathOutput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o
      system_prompt: >
        You are a helpful assistant.
  tasks:
    - name: action_task
      task_no: 1
      description: Add two numbers
      instructions: >
        Add the following two numbers together
        {{number_1}} {{number_2}}
      agent: default
    - name: follow_on_task
      task_no: 2
      description: Perform a final operation
      instructions: >
        Multiply the result of the previous answer by {{number_3}} and then add
        10 to it.
        
        Previous operation:
        {{_action_task}}
      agent: default
