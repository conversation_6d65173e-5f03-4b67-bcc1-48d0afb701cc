import os
import json
import yaml
from dotenv import load_dotenv
from typing import Dict, List, Any, Optional
from semantic_kernel import Kern<PERSON>
from semantic_kernel.functions import kernel_function
from openai import AzureOpenAI
from sk_agents.ska_types import BasePlugin
from logger.agent_loggers import Logger
 
# Initialize logger
logger = Logger()
 
# Load environment variables from .env file
load_dotenv()
 
def load_config(config_path: str) -> Dict:
    try:
        with open(config_path, "r") as file:
            config = yaml.safe_load(file)
            logger.debug(f"Loaded configuration: {config}")
            return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found at {config_path}")
        return {}
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}
 
def load_department_assignment_rules(file_path: str) -> Dict:
    try:
        with open(file_path, "r") as file:
            data = yaml.safe_load(file)
            logger.debug(f"Loaded department assignment rules: {data}")
        return data.get("defect_handling", {})
    except FileNotFoundError:
        logger.error(f"Rules file not found at {file_path}. No rules loaded.")
        return {}
    except Exception as e:
        logger.error(f"Error loading rules from {file_path}: {e}. No rules loaded.")
        return {}
 
class DepartmentAssignmentPlugin(BasePlugin):
    def __init__(self, kernel: Kernel, authorization: Optional[Dict[str, Any]] = None, 
                 extra_data_collector: Optional[Any] = None):
        self.authorization = authorization
        self.extra_data_collector = extra_data_collector
        self.sample_dir = self._find_sample_dir()
 
        self.rules_file = os.path.join(self.sample_dir, "department_assignment_rules.yaml")       
        self.rules = load_department_assignment_rules(self.rules_file)
        
 
    def _find_sample_dir(self) -> str:
        """Find the sample directory using multiple possible paths"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths = [
            os.path.join(os.path.dirname(current_dir), "sample"),  
            os.path.join(current_dir, "sample"),                   
            os.path.join(os.path.dirname(os.path.dirname(current_dir)), "sample"),  
        ]
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found sample directory at: {path}")
                return path
        logger.error("Sample directory not found.")
        return ""
 
    @staticmethod
    def call_openai(prompt: str) -> str:
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        try:
            completion = client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
            )        
            logger.debug("OpenAI API call successful.")
            return completion.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return "Error during API call."
 
    @kernel_function(
        name="assign_department",
        description="Assign departments based on defect type"
    )
    def assign_department(self,recommended_changes) -> str:
        """
        Assigns departments to defects based on defect type using defined rules.
        """
        logger.info("Starting department assignment process.")
 
        prompt = f"""You are responsible for assigning departments to defects based on defect type.
                Department Assignment Rules:
                {json.dumps(self.rules, indent=2)}
                Defects with Recommendations:
                {json.dumps(recommended_changes, indent=2)}
 
                Instructions:
                1. For each defect, determine the appropriate department using `department_assignment_rules`.
                2. Add an `assigned_department` field to each defect entry.
                3. Return the updated list as a valid JSON array."""
 
        
        logger.debug(f"Input prompt for OpenAI: {prompt}")
 
        # Call the OpenAI model with the prompt
        response_json = self.call_openai(prompt)
       
        logger.info(f"Mapped JSON response: {response_json}")
       
        return response_json
 