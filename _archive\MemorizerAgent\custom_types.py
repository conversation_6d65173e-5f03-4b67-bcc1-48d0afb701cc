from pydantic import Field
from semantic_kernel.kernel_pydantic import KernelBaseModel


class Interaction(KernelBaseModel):
    """
    An interaction between a user and the assistant
    """

    user_id: str = Field(description="The ID of the user involved in the interaction")
    message: str = Field(description="The message sent by the user")
    response: str = Field(description="The response sent by the assistant")
