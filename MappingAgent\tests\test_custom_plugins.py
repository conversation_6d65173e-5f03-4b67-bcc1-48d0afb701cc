import sys
import json
import yaml
import unittest
from unittest.mock import patch, MagicMock
import asyncio

# --- Begin: Setup logger module mocking with print statements ---
class PrintLogger:
    """Mock logger class to print log messages to the console."""
    
    def info(self, message):
        """Log an info message."""
        print("INFO:", message)

    def debug(self, message):
        """Log a debug message."""
        print("DEBUG:", message)

    def error(self, message):
        """Log an error message."""
        print("ERROR:", message)

# Insert mock modules into sys.modules so that the import in custom_plugins.py won't fail.
sys.modules['logger'] = MagicMock()
sys.modules['logger.agent_loggers'] = MagicMock()
sys.modules['logger.agent_loggers'].Logger = PrintLogger
# --- End: Setup logger module mocking with print statements ---

from custom_plugins import OrderMappingPlugin  # Adjust the import based on your project structure

class TestOrderMappingPlugin(unittest.TestCase):
    """
    Unit tests for the OrderMappingPlugin class.

    This test suite verifies the functionality of the OrderMappingPlugin,
    which is responsible for mapping order fields from order data to a
    structured format based on predefined SAP rules. The
    tests cover the following key functionalities:

    1. **Field Mapping**:
       - `test_map_order_fields`: Ensures that the `map_order_fields` method
         correctly maps order fields from the provided sample order mapping
         JSON data according to the specified SAP rules. This includes
         verifying that the expected fields such as `order_id`, `product_name`,
         `order_date`, and `item_count` are present in the result.

    Each test method uses mocking to simulate the necessary components and
    isolates the functionality of the OrderMappingPlugin, ensuring that the
    tests are reliable and do not depend on external files or states.
    """

    def test_map_order_fields(self):
        """Test the map_order_fields method of OrderMappingPlugin.

        This test verifies that the method correctly maps order fields
        based on the provided sample order mapping JSON data and SAP rules.
        """
        # Sample order mapping JSON data
        sample_order_mapping_json = json.dumps({
            "emails": [
                {
                    "email_id": 1,
                    "average_confidence_score": 90.4326499,
                    "manual_review_needed": True,
                    "mapped_fields": [
                        {
                            "field": "from",
                            "confidence_score": 95.34968567,
                            "subfields": {
                                "name": {
                                    "value": "Garzon Munoz, Kevin Steven",
                                    "confidence_score": 95.34968567
                                },
                                "email": {
                                    "value": "<EMAIL>",
                                    "confidence_score": 95.34968567
                                }
                            }
                        },
                        {
                            "field": "order_details",
                            "subfields": {
                                "order_number": {
                                    "value": "13373397",
                                    "confidence_score": 95.86212921
                                },
                                "order_date": {
                                    "value": "Friday, 2 May, 2025",
                                    "confidence_score": 94.96853638
                                }
                            }
                        },
                        {
                            "field": "items",
                            "confidence_score": 94.47902679,
                            "subfields": {
                                "description": {
                                    "value": "PREVYMIS 240MG 4X7TAB COL",
                                    "confidence_score": 94.47902679
                                },
                                "quantity": {
                                    "value": "14",
                                    "confidence_score": 95.34706879
                                }
                            }
                        }
                    ]
                }
            ]
        })

        # Sample SAP rules
        sample_rules = '''
        sap_classification_rules:
          - order_id: order_id
          - product_name: product_name
          - order_date: order_date
          - item_count: item_count
        '''

        # Create an instance of OrderMappingPlugin
        plugin = OrderMappingPlugin(None,"/path/")  # Pass None for kernel
        plugin.data = sample_order_mapping_json
        loaded_rules = yaml.safe_load(sample_rules)
        plugin.rules = loaded_rules['sap_classification_rules']  # Ensure this is a list of dicts
        
        # Run the mapping function and check the results
        result = plugin.map_order_fields(sample_order_mapping_json)
        self.assertIn("order_id", result)
        self.assertIn("product_name", result)
        self.assertIn("order_date", result)
        self.assertIn("item_count", result)

if __name__ == '__main__':
    unittest.main(verbosity=2)