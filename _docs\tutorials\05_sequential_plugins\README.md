# Teal Agents Framework Tutorial 05
## Sequential Agents with Plugins
Sequential agents support the same plug-in architecture as Chat agents.

### Example Configuration

```yaml

apiVersion: skagents/v1
kind: Sequential
description: >
  A weather chat agent
service_name: WeatherBot
version: 0.1
input_type: BaseInputWithUserContext
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o
      system_prompt: >
        You are a helpful assistant.
      plugins:
      - WeatherPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default

```

Remember to update our environment to specify the python file which
contains the custom plugin.
**Note:** If you don't specify the plugin name in `TA_PLUGIN_MODULE` below, it will default to look for a `custom_plugins.py` file.

```text
TA_API_KEY=<your-API-key>
TA_SERVICE_CONFIG=_docs/tutorials/05_sequential_plugins/config.yaml
TA_PLUGIN_MODULE=_docs/tutorials/05_sequential_plugins/custom_plugins.py
```