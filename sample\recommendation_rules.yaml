celonis_field_classification_rules:
  - _CASE_KEY
  - _INVOICE_KEY
  - VENDOR_CODE
  - PAYMENT_METHOD
  - RISK_TEXT
  - AGENT_IDENTIFIED_RISK
  - AGENT_COMMNET
  - AGENT_RECOMMENDED_CHANGE

recommendation_rules:
  - rule_id: RC1
    description:
      - "For any unauthorized or unapproved change in payment details, particularly when there is a discrepancy between the payment method on the invoice and the vendor master data, recommend the following steps:"
      - "Steps to Update Payment Method for a Vendor Invoice in SAP:"
      - "Log in to SAP: Open SAP Netweaver Portal and log in with your credentials. Select COMET Launchpad & ECC"
      - "Navigate to Financial Accounting - Document - Change: Use the transaction code FB02 (Change Accounts Payable Document)."
      - "Enter Document Number: Input the document number, company code, and fiscal year you wish to update and press Enter."
      - "Select the Vendor Line Item: Double Click on Vendor Line. Navigate to Additional Data and Payment Terms where you can update Payment Terms."
      - "Update Payment Terms: Select the new Payment Terms."
      - "Save Changes: Click on the save button to update the Payment Terms."

  - rule_id: RC2
    description:
      - "If bank account details have changed without compliance verification, suggest the following actions:"
      - "Verify the legitimacy of the account change with the vendor."
      - "Implement account change alerts to notify relevant personnel."
      - "Establish multi-level approvals for any future changes to bank account details."

  - rule_id: RC3
    description:
      - "If changes were made without audit trails, recommend the following measures:"
      - "Enforce system-wide audit logging to capture all changes."
      - "Conduct periodic audits to ensure compliance with logging policies."
      - "Review past changes to identify any unauthorized modifications."

  - rule_id: RC4
    description:
      - "For any risk labeled 'High', suggest the following immediate actions:"
      - "Initiate an immediate investigation into the identified risk."
      - "Place a temporary hold on related transactions until the risk is mitigated."
      - "Communicate with stakeholders about the risk and the actions being taken."

  - rule_id: RC5
    description:
      - "If the defect indicates unauthorized access, recommend the following steps:"
      - "Review user roles and permissions to identify any unauthorized access."
      - "Enable Multi-Factor Authentication (MFA) for all users."
      - "Restrict critical permissions to only those users who require them for their roles."

  - rule_id: RC6
    description:
      - "For vague or general risks, suggest the following actions:"
      - "Initiate a manual investigation to gather more information about the risk."
      - "Update classification rules to improve detection of similar risks in the future."
      - "Document findings and recommendations for future reference."
