from semantic_kernel.kernel_pydantic import KernelBaseModel


class ValidationReport(KernelBaseModel):
    summary: str
    findings: str
    compliance_status: str


class ValidationInput(KernelBaseModel):
    application_name: str
    document_type: str
    section_name: str
    section_content: str
    system_overview: str
    high_level_requirement: str


class ValidationOutput(KernelBaseModel):
    validation_report: ValidationReport
    improvement_suggestions: str
