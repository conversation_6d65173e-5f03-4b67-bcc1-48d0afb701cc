apiVersion: skagents/v1
# Specifies the API version for Semantic Kernel Agents configuration.
# Currently, this should always be 'skagents/v1'.
kind: AssistantOrchestrator
# Defines the type of resource. Currently, this is always 'AssistantOrchestrator'.
description: >
  # A human-readable description of what this orchestrator is responsible for.
  # Be specific about the types of tasks or domains this orchestrator handles.
  # Example: "This orchestrator routes user queries related to mathematics,
  # weather information, and system process management to specialized agents."
  Replace this with a specific description
  of what this orchestrator does.
service_name: # The unique name for this orchestrator instance. Choose a descriptive name.
              # Example: 'UtilityOrchestrator', 'SupportSystemOrchestrator',
              # YourPlatformOrchestrator
version: 0.1
# The version of this orchestrator configuration. Use semantic versioning (e.g., 0.1, 1.0).
spec:
  fallback_agent: # The name and version of the agent to use when no other agent is suitable.
                  # Format: <AgentName>:<Version>
                  # Example: 'GeneralChatAgent:1.0'
  agent_chooser: # The name and version of the agent that decides which agent should handle a request.
                 # Format: <AgentName>:<Version>
                 # Example: 'AgentSelectorAgent:0.5'
    - YourFirstAgentName:YourFirstAgentVersion
    # The name and version of the first specialized agent managed by this orchestrator.
    # Format: <AgentName>:<Version>
    # Example: 'MathSolver:0.2'
    - YourSecondAgentName:YourSecondAgentVersion
    # The name and version of the second specialized agent.
    # Format: <AgentName>:<Version>
    # Example: 'WeatherReporter:1.1'

    # Continue and add more agents here