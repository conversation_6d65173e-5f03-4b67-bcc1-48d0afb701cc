# GPTeal - Agent Platform

## Overview
This repository serves as the source for agents configured to run on the GPTeal Agent Platform. Only agents intended for **production** should be configured here for development, which will deploy to our Staging and Prod environments. A separate repository will be established for ad-hoc development experimentation in the sandbox environment at a later date.

## Start Here
To get started for local development, refer to the [Setup Instructions](_docs/README.md).

## Folder Structure
Each directory within this repository contains the configuration and custom code required for a specific agent.

```bash
agentgpteal-configs/
├── FirstAgent/         # Configuration and custom code for the FirstAgent deployed to Production
│   ├── config.yaml     # Main configuration file for the FirstAgent
│   ├── README.md       # Documentation for the FirstAgent
│   └── custom_plugins.py # Custom plugins specific to the FirstAgent
├── SecondAgent/
│   ├── config.yaml
│   ├── README.md
│   └── custom_plugins.py
├── ... (more Agent folders)  
├── FirstOrchestrator/  # Configuration for the FirstOrchestrator deployed
│   ├── config.yaml     # Main configuration file for the FirstOrchestrator
│   └── README.md       # Documentation for the FirstOrchestrator
├── SecondOrchestrator/
│   ├── config.yaml
│   └── README.md
├── ... (more Orchestrator folders)  
├── _archive/           # Archive for retired agents and orchestrators
│   ├── ... (retired Agent folders)  
│   └── ... (retired Orchestrator folders)  
├── _build/             # Build items necessary for local development and testing
│   ├── _build/merck_custom_chat_completion_factory.py
│   └── _build/Merck-ICS-PROD-ROOT-CA-G2.crt
├── _docs/              # Documentation including user guides and technical specifications
│   ├── asset/
│   ├── templates/
│   ├── tutorials/
│   ├── TECHNICAL_SPEC.md
│   └── README.md
│   # Most of the below files are related to local development setup and
│   # configuration for the agent functionality
├── .env.example        # Example environment variables for local development
├── .gitignore          # Files and directories to be ignored by Git
├── .merck.yaml         # Merck-specific configuration file for platforms CICD
├── .python-version     # Python version specification for the project
├── agent.py            # Main entry point to run your agent locally
├── CODEOWNERS          # File specifying code ownership of each agent directory
├── Makefile            # Makefile for local development build automation
├── pyproject.toml      # Project metadata and dependencies
├── README.md
```

## Environments

- **Sandbox:** 
  - Short-lived agents and orchestrators.
  - Not configured in this setup, this will be a separate repository. (TBD)
- **Staging:** 
  - Matches the **main** branch.
  - Production-ready environment.
- **Prod:** 
  - Cut release branches on a weekly cadence.

## Development and Deployment Flow
We follow a trunk-based development flow https://trunkbaseddevelopment.com/. Only make pull requests (PRs) to the main branch when you are **ready for production**, which includes approved documentation and change requests.

**Note:** Ensure that a GitHub Team is created for you and any co-developers before beginning agent development: [GitHub Team Setup](https://github.com/orgs/merck-gen/teams). Additionally, you will need a apiGPTeal key to connect to your agent which can be requested at [apiGPTeal Onboarding](https://share.merck.com/spaces/EG/pages/1759994187/apiGPTeal+Onboarding).

### Agent Deployment Steps
1.  **Request a New Agent**
    - To request initial provisioning of your Agent configuration, please fill out the following (example inputs below): [Agent Request Form](https://forge.merck.com/create/templates/default/hai-kap-agents-mono)

        * Environment to make agent:
            `dev`
        * Name of the Agent e.g. VulnerabilityAgent:
            `<AgentName>`
        * Branchname to make Manifest PR with e.g. feature/VulnerabilityAgent:
            `feature/<AgentName>`
        * Agent Version, e.g. 0.1:
            `<VersionNumber>`
        * Agent application name e.g. vulnerability:
            `<application-name>`
        * Agent Image Version (default to latest):
            `<ImageVersion>`
   
2. **Folder/Agent Creation:**
    - After the above form is approved, a folder will be automatically created in this repository and assigned permissions to your GitHub Team via the CODEOWNERS file. Once your new agent folder has been created with the appropriate permissions, you are ready to begin developing your agent!

        <strong>Note:</strong> Currently this automation for creating the folder and adding CODEOWNERS is not implemented, and the Agent Request Form will only setup the backend infrastructure. For now, include these changes manually in the initial PR (e.g. new agent folder with its configs and updating the CODEOWNERS file).

3. **Branching and Development:**
   - Create a new branch from "main" and develop locally in the created folder (e.g., `<AgentNameHere>Agent`). For local development setup instructions, refer to the [Setup Instructions](_docs/README.md) and point the `TA_SERVICE_CONFIG` to the agent's config.yaml file.

4. **Commit and Push:**
   - Commit and push your latest code when you are ready for production.

5. **Code Review:**
   - The assigned code reviewer for your specific agent and an AgentsGPTeal code owner will approve your changes, this is based on the team defined in the earlier form that owns the agent.

6. **Deployment to Staging:**
   - Your changes will be immediately deployed in the Staging environment. You can manually deploy and monitor your agents in ArgoCD: [ArgoCD Access](https://argocd.agents.gpteal.dev.merck.com).

7. **Production Release:**
   - Wait for the weekly releases to Production, where all current agents in the main branch will be moved from Staging to Production.

### Orchestrator Deployment Steps
<i>Coming soon! Please reach out to: <EMAIL> </i>

## Contributing
When creating new agents, please follow the guidelines outlined in the development flow above. Ensure that all changes are well-documented and reviewed before merging into the main branch.


# FastAPI based Multi-Agent Processing API Orchestrator for Manual order Conversion

This document outlines the key aspects of the Multi-Agent Processing API, focusing on its features, workflow, participating agents, operational instructions, dependencies, helper functions, and expected output.

---

## Index

- [Features](#features)
- [Workflow Overview & Participating Agents](#workflow-overview--participating-agents)
- [Running the Main Workflow Orchestrator](#running-the-main-workflow-orchestrator)
- [External File Dependencies](#external-file-dependencies)
- [Helper Functions Explained](#helper-functions-explained)
- [Final Output (All 3 Agents Run Successfully)](#final-output-all-3-agents-run-successfully)

---

## Features

* **Sequential Agent Orchestration**: Manages a pipeline of multiple agents, where the execution of subsequent agents depends on the output of the previous one.
* **Conditional Logic**: Implements decision-making points in the workflow based on data returned by agents (e.g., email classification, confidence scores).
* **External Service Integration**: Communicates with distinct agent microservices via HTTP requests.
* **AWS S3 Integration**: Fetches initial input data from an AWS S3 bucket.
* **Structured Logging**: Provides detailed logs for tracing and debugging the orchestration process.
* **Clear Input/Output Handling**: Uses Pydantic models for defining expected request and response structures.

---

## Workflow Overview & Participating Agents

The orchestrator manages a sequence of up to three agents to process an initial input.

1.  **Initial Data Fetch**: The workflow begins by receiving a POST request. It then calls the `readS3()` function (from `aws_connector.py`) to load initial data, email data (body+attachment data pre-processed through AWS textract service), from an AWS S3 bucket.

2.  **Agent 1: Email Classifying Agent**
    * **Service URL**: `http://localhost:5000/EmailClassifyingAgent/0.1`
    * **Purpose**: To classify the input data (an email). It determines if the input pertains to an "order" or not.
    * **Input**: Email Data from S3, formatted by the `create_input_text` helper function.

3.  **Workflow Breakpoint 1: Classification Check**
    * If Agent 1 classifies the input as **not "order"**: The workflow terminates further agent processing. Only the response from Agent 1 is included in the final output. Agent 2 and Agent 3 are not called.

4.  **Agent 2: Confidence Score Agent**
    * **Service URL**: `http://localhost:6010/ConfidenceScoreAgent/0.1`
    * **Purpose**: If the input is classified as an "order" by Agent 1, this agent calculates an `average_confidence_score` related to the order details.
    * **Input**: The output from Agent 1, formatted by `create_input_text`.

5.  **Workflow Breakpoint 2: Confidence Score Threshold Check**
    * The orchestrator checks if the `average_confidence_score` returned by Agent 2 is greater than **85**.
    * If the score is **85 or below**: The workflow terminates further agent processing. Responses from Agent 1 and Agent 2 are included in the final output. Agent 3 is not called.

6.  **Agent 3: Mapping Agent**
    * **Service URL**: `http://localhost:7000/MappingAgent/0.1`
    * **Purpose**: If the input was an "order" and the confidence score from Agent 2 exceeded 85, this agent performs a data mapping task.
    * **Input**: The output from Agent 2, formatted by `create_input_text`.

7.  **Final Response Compilation**: The orchestrator gathers the responses from all successfully executed agents and returns them in a consolidated JSON structure.

---

## Running the Main Workflow Orchestrator

To run this FastAPI-based orchestrator:

1.  **Ensure Prerequisites**:
    * Python 3.11+ is installed.
    * All dependent agent services (EmailClassifyingAgent, ConfidenceScoreAgent, MappingAgent) are running on their respective `localhost` ports (`5000`, `6010`, `7000`).
    * AWS credentials are configured correctly for S3 access by `aws_connector.py`.
2.  **Install Dependencies**:

    Ensure all dependencies are installed.
    
3.  **Execute the Application**:
    Navigate to the directory containing `main_run_uc1.py` and after activating the .venv environmant, run the below command:
    ```bash
    python main_run_uc1.py
    ```
    * The orchestrator will be accessible at `http://localhost:8000`.
    * Once the UI is loaded, press **Execute** and the agents should start running.

---

## External File Dependencies

This orchestrator relies on the following external Python files/modules, which must be present in the project structure and correctly implemented:

1.  **`aws_connector.py`**:
    * **Purpose**: Contains the `readS3()` function.
    * **Functionality**: This function is responsible for connecting to AWS S3 and fetching the initial data or configuration required by the workflow. The specifics of bucket name, object key, and AWS authentication are handled within this module.
    * **Dependency**: Typically, this module would depend on the `boto3` library.

2.  **`logger/agent_loggers.py`**:
    * **Purpose**: Contains the `Logger` class.
    * **Functionality**: This class is instantiated as `main_use_case1_logger` and used throughout the application to log important events, inputs, outputs, and errors. It helps in tracing the execution flow and debugging.

---

## Helper Functions Explained

1.  **`create_input_text(input_data: dict) -> dict`**:
    * **Purpose**: This function prepares the payload in the specific JSON format expected by the downstream agent services.
    * **How it works**:
        1.  It takes a Python dictionary (`input_data`) as input.
        2.  It converts this dictionary into a JSON string.
        3.  It then wraps this JSON string inside another JSON object, specifically within a "chat_history" list. Each item in this list is a dictionary with a "role" (set to "user") and "content" (the enclosed JSON string).
        * **Example Output Structure**:
            ```json
            {
              "chat_history": [
                {
                  "role": "user",
                  "content": "\"{ 'key': 'value', ... }\"" // The original input_data as an escaped JSON string
                }
              ]
            }
            ```

2.  **`extract_output_raw(json_string: str) -> Optional[Any]`**:
    * **Purpose**: This function extracts the core data payload from the raw JSON response string received from an agent. Agent responses might be wrapped or nested.
    * **How it works**:
        1.  It parses the input `json_string` (which is the raw text response from an agent).
        2.  It looks for a key named `"output_raw"` within the parsed JSON.
        3.  If `"output_raw"` is found, its value (which is often a string containing further JSON, possibly with markdown formatting like ```json ... ```) is extracted.
        4.  It cleans this extracted string by removing common markdown JSON fences (e.g., `` ```json\n ``, ` ``` `) and any leading/trailing whitespace.
        5.  Finally, it parses this cleaned string into a Python dictionary or list.
        6.  If `"output_raw"` is not found, it returns `None`.

---

## Final Output (All 3 Agents Run Successfully)

When the input is classified as "order" by Agent 1, and Agent 2 returns an `average_confidence_score` greater than 85, all three agents will execute successfully. The final JSON output from the `/master/agent` endpoint will look like this:

```json
{
  "agent1_response": {
    // Processed and extracted JSON output from Agent 1
    // Example: "classification": "order", "extracted_details": { ... }
  },
  "agent2_response": {
    // Processed and extracted JSON output from Agent 2
    // Example: "average_confidence_score": 92, "validation_results": [ ... ]
  },
  "agent3_response": {
    // Processed and extracted JSON output from Agent 3
    // Example: "material_id": "102589564", "mapped_data": { ... }
  }
}

```


# FastAPI Based Multi-Agent Orchestration API for Defect Analysis and Assignment

This document describes the core functionality, architecture, execution workflow, and helper utilities for the Defect Analysis Multi-Agent Orchestration API. It outlines how the application communicates sequentially with three agent services to analyze defects, recommend changes, and assign responsibility.

---

## Index

- [Features](#features)  
- [Workflow Overview & Participating Agents](#workflow-overview--participating-agents)  
- [Running the Orchestrator](#running-the-orchestrator)  
- [External File Dependencies](#external-file-dependencies)  
- [Helper Functions Explained](#helper-functions-explained)  
- [Final Output](#final-output)  

---

## Features

* **Sequential Agent Processing**: Executes three agents in order—each agent depends on the processed output of the previous one.  
* **Default Input Fallback**: Uses a default input JSON file if no input is passed via the API request.  
* **Simple API Interface**: Provides a single POST endpoint (`/master/agent`) to trigger the complete workflow.  
* **FastAPI + Pydantic**: Clean schema validation and type checking.  
* **Structured Responses**: Consolidates final output from all three agents into a unified JSON structure.  
* **Output Parsing Utility**: Parses nested and wrapped JSON responses using `extract_output_raw`.  

---

## Workflow Overview & Participating Agents

The orchestrator coordinates three backend agent services. Each performs a distinct task in analyzing and resolving software/system defects.

1. **Initial Input Handling**:
   * Accepts an optional `input_text` in the request body.
   * If not provided, defaults to loading a predefined input JSON (`use_case2_celonis_input.json`).

2. **Agent 1: Identify Defect Agent**
   * **Service URL**: `http://localhost:5000/IdentifyDefectAgent/0.1`  
   * **Purpose**: Detects and classifies defects in the provided input.  
   * **Input**: Raw or default data, wrapped using `create_input_text`.  
   * **Output**: JSON structure detailing the identified defects.  

3. **Agent 2: Recommend Defect Changes Agent**
   * **Service URL**: `http://localhost:6010/RecommendDefectChangesAgent/0.1`  
   * **Purpose**: Suggests possible resolutions for the identified defects.  
   * **Input**: Parsed output of Agent 1.  
   * **Output**: JSON with recommended changes for each defect.  

4. **Agent 3: Department Assignment Agent**
   * **Service URL**: `http://localhost:7000/DepartmentAssignmentAgent/0.1`  
   * **Purpose**: Assigns each defect or issue to the appropriate internal department.  
   * **Input**: Parsed output of Agent 2.  
   * **Output**: Final JSON indicating department assignments.  

---

## Running the Orchestrator

### Prerequisites

* Python 3.11+  
1. Ensure all agents are independently running at their specified localhost ports:
  * `IdentifyDefectAgent` on `5000`
  * `RecommendDefectChangesAgent` on `6010`
  * `DepartmentAssignmentAgent` on `7000`  

2.  **Install Dependencies**:

    Ensure all dependencies are installed.
    
3.  **Execute the Application**:
    Navigate to the directory containing `main_run_uc1.py` and after activating the .venv environmant, run the below command:
    ```bash
    python main_run_uc1.py
    ```
    * The orchestrator will be accessible at `http://localhost:8000`.
    * Once the UI is loaded, press **Execute** and the agents should start running.

---

## External File Dependencies

This orchestrator relies on the following external Python files/modules, which must be present in the project structure and correctly implemented:

1.  **`sample/use_case2_celonis_input.json`**:
    * **Purpose**: Default input used when the API is called without input data.
    
2.  **`logger/agent_loggers.py`**:
    * **Purpose**: Contains the `Logger` class.
    * **Functionality**: This class is instantiated as `main_use_case1_logger` and used throughout the application to log important events, inputs, outputs, and errors. It helps in tracing the execution flow and debugging.

---

## Helper Functions Explained

1.  **`create_input_text(input_data: dict) -> dict`**:
    * **Purpose**: This function prepares the payload in the specific JSON format expected by the downstream agent services.
    * **How it works**:
        1.  It takes a Python dictionary (`input_data`) as input.
        2.  It converts this dictionary into a JSON string.
        3.  It then wraps this JSON string inside another JSON object, specifically within a "chat_history" list. Each item in this list is a dictionary with a "role" (set to "user") and "content" (the enclosed JSON string).
        * **Example Output Structure**:
            ```json
            {
              "chat_history": [
                {
                  "role": "user",
                  "content": "\"{ 'key': 'value', ... }\"" // The original input_data as an escaped JSON string
                }
              ]
            }
            ```

2.  **`extract_output_raw(json_string: str) -> Optional[Any]`**:
    * **Purpose**: This function extracts the core data payload from the raw JSON response string received from an agent. Agent responses might be wrapped or nested.
    * **How it works**:
        1.  It parses the input `json_string` (which is the raw text response from an agent).
        2.  It looks for a key named `"output_raw"` within the parsed JSON.
        3.  If `"output_raw"` is found, its value (which is often a string containing further JSON, possibly with markdown formatting like ```json ... ```) is extracted.
        4.  It cleans this extracted string by removing common markdown JSON fences (e.g., `` ```json\n ``, ` ``` `) and any leading/trailing whitespace.
        5.  Finally, it parses this cleaned string into a Python dictionary or list.
        6.  If `"output_raw"` is not found, it returns `None`.

---

## Final Output (All 3 Agents Run Successfully)

Upon successful execution of all three agents, the orchestrator returns a combined response in this format:

```json
{
  "agent1_response": {
    // Processed and extracted output from IdentifyDefectAgent
  },
  "agent2_response": {
    // Processed and extracted output from RecommendDefectChangesAgent
  },
  "agent3_response": {
    // Processed and extracted output from DepartmentAssignmentAgent
  }
}
```

