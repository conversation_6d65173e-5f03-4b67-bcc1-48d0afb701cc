import os
import sys
import json
import unittest
import asyncio
from unittest.mock import patch, mock_open, MagicMock

# --- Begin: Setup logger module mocking with print statements ---
class PrintLogger:
    def info(self, message):
        print("INFO:", message)

    def debug(self, message):
        print("DEBUG:", message)

    def error(self, message):
        print("ERROR:", message)

# Insert mock modules into sys.modules so that the import in custom_plugins.py won't fail.
sys.modules['logger'] = MagicMock()
sys.modules['logger.agent_loggers'] = MagicMock()
sys.modules['logger.agent_loggers'].Logger = PrintLogger
# --- End: Setup logger module mocking with print statements ---

# Ensure our parent directory is in sys.path for module imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, ".."))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from custom_plugins import EmailClassifyingPlugin

class TestEmailClassifyingPlugin(unittest.TestCase):

    """
    Tests that the classify_email method correctly identifies an email as an order type.
    
    This test verifies that when provided with a email data containing
    order-related information (such as purchase order numbers, product details, quantities,
    prices, and delivery instructions), the classify_email method properly classifies it
    as an "order" type email.
    
    Expected outcome: The classify_email method should return a classification indicating
    this is an order-type email rather than another category.

    """
    
    
    def test_classify_email_returns_order_classification(self):
        """
        Test that the classify_email method correctly classifies an email as an order.
        """
        # Sample email data structure

        sample_email_data_structure = {
            "email_id" :"1",
            "email_data": [
        {
            "key": "From:",
            "Key_confidence_score": 95.34968567,
            "value": "Garzon Munoz, Kevin Steven <<EMAIL>>",
            "Value_confidence_score": 95.34968567
        },
        {
            "key": "Sent:",
            "Key_confidence_score": 95.60166931,
            "value": "Tuesday, May 6, 2025 1:47 AM",
            "Value_confidence_score": 95.60166931
        },
        {
            "key": "To:",
            "Key_confidence_score": 91.01599121,
            "value": "ColombiaBSCSO",
            "Value_confidence_score": 91.01599121
        },
        {
            "key": "FW:",
            "Key_confidence_score": 85.81208801,
            "value": "Orden de Compra 13373397 - BOGOTA-MERCK SHARP & DOHME COLOMBIA",
            "Value_confidence_score": 85.81208801
        },
        {
            "key": "SAS",
            "Key_confidence_score": 88.86192322,
            "value": "2025-05-09",
            "Value_confidence_score": 88.86192322
        },
        {
            "key": "Attachments:",
            "Key_confidence_score": 95.13069153,
            "value": "Orden de Compra.pdf",
            "Value_confidence_score": 95.13069153
        },
        {
            "key": "Por favor ingresar",
            "Key_confidence_score": 61.59713364,
            "value": "OC",
            "Value_confidence_score": 61.59713364
        },
        {
            "key": "Fecha y Hora de Entrega",
            "Key_confidence_score": 80.0,
            "value": "2025-05-09 Hora:11:00",
            "Value_confidence_score": 80.0
        },
        {
            "key": "CLIENTE",
            "Key_confidence_score": 80.0,
            "value": "CRUZ VERDE_INST",
            "Value_confidence_score": 80.0
        },
        {
            "key": "DESTINO",
            "Key_confidence_score": 94.67159271,
            "value": "COTA INSTITUCIONAL - CEDI ORION LOTE SAN GREGORIO COTA",
            "Value_confidence_score": 94.67159271
        },
        {
            "key": "Direcci\u00f3n de Entrega",
            "Key_confidence_score": 73.12314606,
            "value": "Centro de Distribuci\u00f3n Orion - Parque Log\u00edstico Constellation, Vereda Vuelta Grande Km 1 v\u00eda potrero chico 1 COLOMBIA-COTA",
            "Value_confidence_score": 73.12314606
        },
        {
            "key": "COD PAYER",
            "Key_confidence_score": 95.49247742,
            "value": "3000009063",
            "Value_confidence_score": 95.49247742
        },
        {
            "key": "COD SOLD-TO",
            "Key_confidence_score": 95.73278046,
            "value": "50084863",
            "Value_confidence_score": 95.73278046
        },
        {
            "key": "COD SHIP TO",
            "Key_confidence_score": 95.6811676,
            "value": "50084863",
            "Value_confidence_score": 95.6811676
        },
        {
            "key": "INSTRUCCI\u00d3N",
            "Key_confidence_score": 95.23529053,
            "value": "052 CITA PREVIA",
            "Value_confidence_score": 95.23529053
        },
        {
            "key": "ORDEN DE COMPRA",
            "Key_confidence_score": 95.86212921,
            "value": "13373397",
            "Value_confidence_score": 95.86212921
        },
        {
            "key": "Fecha Cita Entrega",
            "Key_confidence_score": 94.97883606,
            "value": "viernes, 09 de mayo de 2025",
            "Value_confidence_score": 94.97883606
        },
        {
            "key": "VALOR TOTAL",
            "Key_confidence_score": 94.92521667,
            "value": "$ 293,018,432",
            "Value_confidence_score": 94.92521667
        },
        {
            "key": "COD PARA GRABAR",
            "Key_confidence_score": 95.10430908,
            "value": "1041849",
            "Value_confidence_score": 95.10430908
        },
        {
            "key": "DESCRIPCION",
            "Key_confidence_score": 94.47902679,
            "value": "PREVYMIS 240MG 4X7TAB COL",
            "Value_confidence_score": 94.47902679
        },
        {
            "key": "CANT",
            "Key_confidence_score": 95.34706879,
            "value": "14",
            "Value_confidence_score": 95.34706879
        },
        {
            "key": "VALOR C/U",
            "Key_confidence_score": 94.78562164,
            "value": "20,929,888",
            "Value_confidence_score": 94.78562164
        },
        {
            "key": "VALOR TOTAL",
            "Key_confidence_score": 94.76661682,
            "value": "293,018,432",
            "Value_confidence_score": 94.76661682
        },
        {
            "key": "From:",
            "Key_confidence_score": 94.79012299,
            "value": "OrderManager <<EMAIL>>",
            "Value_confidence_score": 94.79012299
        },
        {
            "key": "Sent:",
            "Key_confidence_score": 94.96853638,
            "value": "Friday, 2 May, 2025 6:29 PM",
            "Value_confidence_score": 94.96853638
        },
        {
            "key": "To:",
            "Key_confidence_score": 88.23873138,
            "value": "Duarte Montero, Fernando <<EMAIL>>; Garzon Munoz, <NAME_EMAIL>>",
            "Value_confidence_score": 88.23873138
        },
        {
            "key": "Cc:",
            "Key_confidence_score": 95.03250885,
            "value": "<EMAIL>",
            "Value_confidence_score": 95.03250885
        },
        {
            "key": "Subject:",
            "Key_confidence_score": 89.90779114,
            "value": "Orden de Compra 13373397 - BOGOTA-MERCK SHARP & DOHME 2025-05-09",
            "Value_confidence_score": 89.90779114
        },
        {
            "key": "COLOMBIA SAS -",
            "Key_confidence_score": 46.3209877,
            "value": "",
            "Value_confidence_score": 46.3209877
        },
        {
            "key": "EXTERNAL EMAIL-",
            "Key_confidence_score": 94.30007935,
            "value": "Use caution with any links or file attachments.",
            "Value_confidence_score": 94.30007935
        },
        {
            "key": "soluci\u00f3n",
            "Key_confidence_score": 46.00845337,
            "value": "00085 del 8 de abril de 2022.",
            "Value_confidence_score": 46.00845337
        },
        {
            "key": "Fecha:",
            "Key_confidence_score": 67.96354675,
            "value": "",
            "Value_confidence_score": 67.96354675
        },
        {
            "key": "ordialmente;",
            "Key_confidence_score": 80.01728821,
            "value": "uipo de Compras",
            "Value_confidence_score": 80.01728821
        }
    ],
    "attachment_data": [
        {
            "key": "1 de",
            "Key_confidence_score": 48.06734085,
            "value": "1",
            "Value_confidence_score": 48.06734085
        },
        {
            "key": "N\u00b0 ORDEN DE COMPRA",
            "Key_confidence_score": 70.75467682,
            "value": "13373397",
            "Value_confidence_score": 70.75467682
        },
        {
            "key": "Nit:",
            "Key_confidence_score": 94.15236664,
            "value": "800149695-1",
            "Value_confidence_score": 94.15236664
        },
        {
            "key": "Proveedor:",
            "Key_confidence_score": 94.64997864,
            "value": "BOGOTA-MERCK SHARP & DOHME COLOMBIA SAS",
            "Value_confidence_score": 94.64997864
        },
        {
            "key": "Comprador:",
            "Key_confidence_score": 95.13069153,
            "value": "Pena Forero, Maria Alejandra",
            "Value_confidence_score": 95.13069153
        },
        {
            "key": "Direcci\u00f3n:",
            "Key_confidence_score": 94.75299835,
            "value": "Cra 12 # 96-32",
            "Value_confidence_score": 94.75299835
        },
        {
            "key": "Nit:",
            "Key_confidence_score": 94.57089996,
            "value": "860002392-1",
            "Value_confidence_score": 94.57089996
        },
        {
            "key": "Forma de Pago:",
            "Key_confidence_score": 95.1484375,
            "value": "PAGO A 120 DIAS",
            "Value_confidence_score": 95.1484375
        },
        {
            "key": "Tel\u00e9fono:",
            "Key_confidence_score": 94.98129272,
            "value": "4924860",
            "Value_confidence_score": 94.98129272
        },
        {
            "key": "Sucursal:",
            "Key_confidence_score": 94.86873627,
            "value": "BOGOTA",
            "Value_confidence_score": 94.86873627
        },
        {
            "key": "Moneda:",
            "Key_confidence_score": 95.10894775,
            "value": "COP",
            "Value_confidence_score": 95.10894775
        },
        {
            "key": "FAX:",
            "Key_confidence_score": 94.86826324,
            "value": "4924860",
            "Value_confidence_score": 94.86826324
        },
        {
            "key": "Contacto:",
            "Key_confidence_score": 94.89185333,
            "value": "CAROLINA CASTRO",
            "Value_confidence_score": 94.89185333
        },
        {
            "key": "Fecha OC:",
            "Key_confidence_score": 95.31964874,
            "value": "2025-05-02",
            "Value_confidence_score": 95.31964874
        },
        {
            "key": "Direcci\u00f3n:",
            "Key_confidence_score": 94.49906158,
            "value": "CL 127 a 53 A 45 TORRE 3 PISO 8",
            "Value_confidence_score": 94.49906158
        },
        {
            "key": "Fecha Elaboraci\u00f3n:",
            "Key_confidence_score": 95.09464264,
            "value": "2025-05-02",
            "Value_confidence_score": 95.09464264
        },
        {
            "key": "Tel\u00e9fono:",
            "Key_confidence_score": 92.15746307,
            "value": "",
            "Value_confidence_score": 92.15746307
        },
        {
            "key": "Se\u00f1or Proveedor, recuerde:",
            "Key_confidence_score": 88.28256989,
            "value": "",
            "Value_confidence_score": 88.28256989
        },
        {
            "key": "Observaciones:",
            "Key_confidence_score": 93.08187103,
            "value": "02 05 2025",
            "Value_confidence_score": 93.08187103
        },
        {
            "key": "Pedido General",
            "Key_confidence_score": 55.4528656,
            "value": "",
            "Value_confidence_score": 55.4528656
        },
        {
            "key": "Bodega:",
            "Key_confidence_score": 93.37238312,
            "value": "1058 CRUZVERDE CEDI_COTA ALMACEN_PRINCIPAL",
            "Value_confidence_score": 93.37238312
        },
        {
            "key": "Direcci\u00f3n:",
            "Key_confidence_score": 94.13596344,
            "value": "Centro de Distribuci\u00f3n Orion Parque Log\u00edstico Constellation Vereda Vuelta Grande Km 1 v\u00eda potrero chico 1 COLOMBIA-COTA",
            "Value_confidence_score": 94.13596344
        },
        {
            "key": "Fecha:",
            "Key_confidence_score": 89.9910202,
            "value": "2025-05-09",
            "Value_confidence_score": 89.9910202
        },
        {
            "key": "Hora:",
            "Key_confidence_score": 89.16079712,
            "value": "11:00",
            "Value_confidence_score": 89.16079712
        },
        {
            "key": "Art\u00edculo",
            "Key_confidence_score": 94.75588226,
            "value": "547522",
            "Value_confidence_score": 94.75588226
        },
        {
            "key": "C\u00f3digo CR 11 Co",
            "Key_confidence_score": 92.65549469,
            "value": "547522",
            "Value_confidence_score": 92.65549469
        },
        {
            "key": "Descripci\u00f3n",
            "Key_confidence_score": 93.44135284,
            "value": "PREVYMIS 240MG TAB REC CAJ X 28",
            "Value_confidence_score": 93.44135284
        },
        {
            "key": "Unids. Solicit",
            "Key_confidence_score": 94.97534943,
            "value": "14",
            "Value_confidence_score": 94.97534943
        },
        {
            "key": "Unids.",
            "Key_confidence_score": 94.99485016,
            "value": "CAJ",
            "Value_confidence_score": 94.99485016
        },
        {
            "key": "Precio Unitario F.",
            "Key_confidence_score": 94.16201019,
            "value": "20.929.888",
            "Value_confidence_score": 94.16201019
        },
        {
            "key": "Cantidad (Recibo)",
            "Key_confidence_score": 91.32650757,
            "value": "",
            "Value_confidence_score": 91.32650757
        },
        {
            "key": "%IVA",
            "Key_confidence_score": 95.14689636,
            "value": "0%",
            "Value_confidence_score": 95.14689636
        },
        {
            "key": "Costo",
            "Key_confidence_score": 94.9634552,
            "value": "293.018.432",
            "Value_confidence_score": 94.9634552
        },
        {
            "key": "Mto.IVA",
            "Key_confidence_score": 91.57093048,
            "value": "",
            "Value_confidence_score": 91.57093048
        },
        {
            "key": "Costo Total",
            "Key_confidence_score": 94.24774933,
            "value": "293.018.432",
            "Value_confidence_score": 94.24774933
        },
        {
            "key": "SUB TOTAL:",
            "Key_confidence_score": 94.79489899,
            "value": "293.018.432",
            "Value_confidence_score": 94.79489899
        },
        {
            "key": "IVA TOTAL:",
            "Key_confidence_score": 91.89688873,
            "value": "",
            "Value_confidence_score": 91.89688873
        },
        {
            "key": "TOTALES:",
            "Key_confidence_score": 94.53596497,
            "value": "293.018.432",
            "Value_confidence_score": 94.53596497
        },
        {
            "key": "Elaborado por:",
            "Key_confidence_score": 94.07272339,
            "value": "MARIA.PENAR",
            "Value_confidence_score": 94.07272339
        },
        {
            "key": "Aprobado por:",
            "Key_confidence_score": 94.45423126,
            "value": "adriana.osorio",
            "Value_confidence_score": 94.45423126
        }
    ]

}
                     
        # Create the plugin instance
        plugin = EmailClassifyingPlugin()
                
        
        result = asyncio.run(plugin.classify_email(sample_email_data_structure))
    
        #  Decode the JSON string 'result'
        result_json = json.loads(json.loads(result))
        # Verify the classification result
        self.assertEqual(result_json["classification"], "order")
                
                
    

    """
    Tests that the classify_email method correctly identifies an email as NOT being an order type.
    
    This test verifies that when provided with a sample email data structure containing
    non-order related information (such as meeting invitations, agenda details, and
    general communications), the classify_email method properly classifies it as a
    "not order" type email.
    
    Expected outcome: The classify_email method should return a classification indicating
    this is a "not order" type email, demonstrating the method can correctly distinguish
    between order and non-order emails.
    
    This serves as a negative test case to complement the positive order classification test.
    """               
    

    def test_classify_email_returns_notorder_classification(self):
        """
        Test that the classify_email method correctly classifies an email as an not order.
        """
        # Sample email data structure
        sample_email_data_structure = {
      "email_id" :"2",
      "email_data": [
        {
            "key": "From:",
            "Key_confidence_score": 95.33609772,
            "value": "Martinez, Ana <<EMAIL>>",
            "Value_confidence_score": 95.33609772
        },
        {
            "key": "Sent:",
            "Key_confidence_score": 95.56770325,
            "value": "Tuesday, May 6, 2025 10:23 AM",
            "Value_confidence_score": 95.56770325
        },
        {
            "key": "To:",
            "Key_confidence_score": 91.02088928,
            "value": "ColombiaTechTeam",
            "Value_confidence_score": 91.02088928
        },
        {
            "key": "Subject:",
            "Key_confidence_score": 85.56860352,
            "value": "Invitación: Reunión trimestral de equipo - Mayo 2025",
            "Value_confidence_score": 85.56860352
        },
        {
            "key": "Attachments:",
            "Key_confidence_score": 95.21224213,
            "value": "Agenda_Reunion_Q2.pdf",
            "Value_confidence_score": 95.21224213
        },
        {
            "key": "Estimados colegas,",
            "Key_confidence_score": 87.35475159,
            "value": "",
            "Value_confidence_score": 87.35475159
        },
        {
            "key": "Por medio de la presente",
            "Key_confidence_score": 57.86337280,
            "value": "les invito a nuestra reunión trimestral de equipo",
            "Value_confidence_score": 57.86337280
        },
        {
            "key": "FECHA",
            "Key_confidence_score": 87.63548279,
            "value": "15 de mayo de 2025",
            "Value_confidence_score": 87.63548279
        },
        {
            "key": "Lugar",
            "Key_confidence_score": 88.74564362,
            "value": "Sala de conferencias principal - Piso 8",
            "Value_confidence_score": 88.74564362
        },
        {
            "key": "HORA",
            "Key_confidence_score": 94.79252625,
            "value": "9:00 AM - 12:00 PM",
            "Value_confidence_score": 94.79252625
        },
        {
            "key": "AGENDA",
            "Key_confidence_score": 95.63815308,
            "value": "1. Bienvenida y actualizaciones",
            "Value_confidence_score": 95.63815308
        },
        {
            "key": "",
            "Key_confidence_score": 95.65637207,
            "value": "2. Resultados del trimestre anterior",
            "Value_confidence_score": 95.65637207
        },
        {
            "key": "",
            "Key_confidence_score": 95.68052673,
            "value": "3. Objetivos para el próximo trimestre",
            "Value_confidence_score": 95.68052673
        },
        {
            "key": "",
            "Key_confidence_score": 95.40614319,
            "value": "4. Presentación de nuevos proyectos",
            "Value_confidence_score": 95.40614319
        },
        {
            "key": "",
            "Key_confidence_score": 95.75287628,
            "value": "5. Sesión de preguntas y respuestas",
            "Value_confidence_score": 95.75287628
        },
        {
            "key": "IMPORTANTE",
            "Key_confidence_score": 95.21823120,
            "value": "Por favor confirmar asistencia antes del 10 de mayo",
            "Value_confidence_score": 95.21823120
        },
        {
            "key": "Contacto",
            "Key_confidence_score": 95.08289337,
            "value": "Ana Martinez, ext. 4567",
            "Value_confidence_score": 95.08289337
        },
        {
            "key": "Notas",
            "Key_confidence_score": 95.16687012,
            "value": "Se servirá un almuerzo ligero al finalizar la reunión",
            "Value_confidence_score": 95.16687012
        },
        {
            "key": "Recordatorio",
            "Key_confidence_score": 94.07054138,
            "value": "Traer sus reportes de actividades del último mes",
            "Value_confidence_score": 94.07054138
        },
        {
            "key": "From:",
            "Key_confidence_score": 94.47128296,
            "value": "Sistema de Notificaciones <<EMAIL>>",
            "Value_confidence_score": 94.47128296
        },
        {
            "key": "Cordialmente,",
            "Key_confidence_score": 80.00000000,
            "value": "Ana Martinez",
            "Value_confidence_score": 80.00000000
        },
        {
            "key": "Sent:",
            "Key_confidence_score": 95.03112793,
            "value": "Monday, 1 May, 2025 8:15 AM",
            "Value_confidence_score": 95.03112793
        },
        {
            "key": "To:",
            "Key_confidence_score": 86.05175781,
            "value": "Martinez, Ana <<EMAIL>>",
            "Value_confidence_score": 86.05175781
        },
        {
            "key": "Cc:",
            "Key_confidence_score": 95.02873993,
            "value": "<EMAIL>",
            "Value_confidence_score": 95.02873993
        },
        {
            "key": "Subject:",
            "Key_confidence_score": 85.44535065,
            "value": "Recordatorio: Actualización de contraseña requerida",
            "Value_confidence_score": 85.44535065
        },
        {
            "key": "EXTERNAL EMAIL-",
            "Key_confidence_score": 93.07389832,
            "value": "Use caution with any links or file attachments.",
            "Value_confidence_score": 93.07389832
        },
        {
            "key": "Estimado usuario:",
            "Key_confidence_score": 68.36287689,
            "value": "Su contraseña expirará en 7 días. Por favor actualícela.",
            "Value_confidence_score": 68.36287689
        },
        {
            "key": "Departamento de TI",
            "Key_confidence_score": 87.97211456,
            "value": "Para asistencia contacte a la mesa de ayuda",
            "Value_confidence_score": 87.97211456
        },
        {
            "key": "Este correo es generado automáticamente. Por favor no responda a este mensaje. Si necesita ayuda, contacte al departamento de soporte técnico.",
            "Key_confidence_score": 48.80320358,
            "value": "",
            "Value_confidence_score": 48.80320358
        }
    ],
    "attachment_data": [
        {
            "key": "AGENDA DE REUNIÓN TRIMESTRAL",
            "Key_confidence_score": 91.35095215,
            "value": "Q2 2025",
            "Value_confidence_score": 91.35095215
        },
        {
            "key": "Página",
            "Key_confidence_score": 93.67271423,
            "value": "1 de 3",
            "Value_confidence_score": 93.67271423
        },
        {
            "key": "FECHA",
            "Key_confidence_score": 64.89479828,
            "value": "15 de mayo de 2025",
            "Value_confidence_score": 64.89479828
        },
        {
            "key": "Hora:",
            "Key_confidence_score": 95.23593903,
            "value": "9:00 AM - 12:00 PM",
            "Value_confidence_score": 95.23593903
        },
        {
            "key": "Lugar:",
            "Key_confidence_score": 95.20404053,
            "value": "Sala de conferencias principal - Piso 8",
            "Value_confidence_score": 95.20404053
        },
        {
            "key": "PARTICIPANTES ESPERADOS",
            "Key_confidence_score": 87.26724243,
            "value": "25",
            "Value_confidence_score": 87.26724243
        },
        {
            "key": "Facilitador",
            "Key_confidence_score": 93.72653961,
            "value": "Ana Martinez",
            "Value_confidence_score": 93.72653961
        },
        {
            "key": "Secretario:",
            "Key_confidence_score": 80.00000000,
            "value": "Carlos Rodriguez",
            "Value_confidence_score": 80.00000000
        },
        {
            "key": "Departamentos",
            "Key_confidence_score": 84.44598389,
            "value": "Ventas, Marketing, Finanzas, RH",
            "Value_confidence_score": 84.44598389
        },
        {
            "key": "Objetivos de la reunión:",
            "Key_confidence_score": 81.34117889,
            "value": "",
            "Value_confidence_score": 81.34117889
        },
        {
            "key": "1.",
            "Key_confidence_score": 80.41027069,
            "value": "Revisar resultados del Q1 2025",
            "Value_confidence_score": 80.41027069
        },
        {
            "key": "2.",
            "Key_confidence_score": 95.15175629,
            "value": "Establecer metas para Q2 2025",
            "Value_confidence_score": 95.15175629
        },
        {
            "key": "3.",
            "Key_confidence_score": 95.04530334,
            "value": "Presentar nuevas iniciativas",
            "Value_confidence_score": 95.04530334
        },
        {
            "key": "4.",
            "Key_confidence_score": 95.39957428,
            "value": "Discutir desafíos actuales",
            "Value_confidence_score": 95.39957428
        },
        {
            "key": "Cronograma:",
            "Key_confidence_score": 95.50454712,
            "value": "",
            "Value_confidence_score": 95.50454712
        },
        {
            "key": "9:00 - 9:15",
            "Key_confidence_score": 95.68000793,
            "value": "Bienvenida y café",
            "Value_confidence_score": 95.68000793
        },
        {
            "key": "9:15 - 10:00",
            "Key_confidence_score": 95.35633850,
            "value": "Presentación de resultados Q1",
            "Value_confidence_score": 95.35633850
        },
        {
            "key": "10:00 - 10:45",
            "Key_confidence_score": 95.23609161,
            "value": "Discusión de metas Q2",
            "Value_confidence_score": 95.23609161
        },
        {
            "key": "10:45 - 11:00",
            "Key_confidence_score": 95.21885681,
            "value": "Pausa para café",
            "Value_confidence_score": 95.21885681
        },
        {
            "key": "11:00 - 11:30",
            "Key_confidence_score": 95.55596161,
            "value": "Nuevas iniciativas",
            "Value_confidence_score": 95.55596161
        },
        {
            "key": "11:30 - 12:00",
            "Key_confidence_score": 95.56781769,
            "value": "Preguntas y cierre",
            "Value_confidence_score": 95.56781769
        },
        {
            "key": "Materiales",
            "Key_confidence_score": 47.49020767,
            "value": "Presentación PowerPoint, Informes trimestrales",
            "Value_confidence_score": 47.49020767
        },
        {
            "key": "Preparación requerida",
            "Key_confidence_score": 52.39934158,
            "value": "Revisar informes enviados previamente",
            "Value_confidence_score": 52.39934158
        },
        {
            "key": "NOTAS IMPORTANTES",
            "Key_confidence_score": 69.10733795,
            "value": "Traer computadoras portátiles",
            "Value_confidence_score": 69.10733795
        },
        {
            "key": "PRÓXIMA REUNIÓN",
            "Key_confidence_score": 95.22023010,
            "value": "15 de agosto de 2025",
            "Value_confidence_score": 95.22023010
        },
        {
            "key": "CONTACTO PARA DUDAS",
            "Key_confidence_score": 94.92195129,
            "value": "<EMAIL>",
            "Value_confidence_score": 94.92195129
        },
        {
            "key": "RECORDATORIO",
            "Key_confidence_score": 95.46615601,
            "value": "Confirmar asistencia",
            "Value_confidence_score": 95.46615601
        },
        {
            "key": "Notas adicionales:",
            "Key_confidence_score": 92.36311340,
            "value": "Se compartirán las minutas después de la reunión",
            "Value_confidence_score": 92.36311340
        },
        {
            "key": "Preparado por:",
            "Key_confidence_score": 94.96646881,
            "value": "DEPARTAMENTO DE RECURSOS HUMANOS",
            "Value_confidence_score": 94.96646881
        }
    ]
}
        # Create the plugin instance
        plugin = EmailClassifyingPlugin()
                
        result = asyncio.run(plugin.classify_email(sample_email_data_structure))
    
        #  Decode the JSON string 'result'
        result_json = json.loads(json.loads(result))
        # Verify the classification result
        self.assertEqual(result_json["classification"], "not order")
        
        
       
                
if __name__ == '__main__':
    unittest.main(verbosity=2)

    