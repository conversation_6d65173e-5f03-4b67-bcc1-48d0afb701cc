import os
import json
import yaml
import re
from dotenv import load_dotenv
from typing import Any, Dict, Optional
from semantic_kernel import <PERSON><PERSON>
from semantic_kernel.functions import kernel_function
from logger.agent_loggers import Logger
from sk_agents.ska_types import BasePlugin
from openai import AzureOpenAI

# Load environment variables from .env file
load_dotenv()

# Initialize logger
logger = Logger()

def load_rules(file_path: str) -> Dict:
    """Load rules from a YAML file."""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            rules = yaml.safe_load(file)
            logger.debug(f"Loaded rules: {rules}")
            return rules
    except FileNotFoundError:
        logger.error(f"Rules file not found at {file_path}. No rules loaded.")
        return {}
    except Exception as e:
        logger.error(f"Error loading rules from {file_path}: {e}. No rules loaded.")
        return {}

class RecommendDefectChangesAgent(BasePlugin):
    """Plugin to recommend changes based on detected defects and business rules using LLM."""

    def __init__(self, kernel: Kernel, authorization: Optional[Dict[str, Any]] = None, 
                 extra_data_collector: Optional[Any] = None):
        self.authorization = authorization
        self.extra_data_collector = extra_data_collector
    
        self.sample_dir = self._find_sample_dir()
        self.rules_file = os.path.join(self.sample_dir, 'recommendation_rules.yaml')
        self.rules = load_rules(self.rules_file)
        

    def _find_sample_dir(self) -> str:
        """Find the sample directory using multiple possible paths."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths = [
            os.path.join(os.path.dirname(current_dir), "sample"),  
            os.path.join(current_dir, "sample"),                  
            os.path.join(os.path.dirname(os.path.dirname(current_dir)), "sample"),  
        ]
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found sample directory at: {path}")
                return path
        return ""

    @staticmethod
    def call_openai(prompt: str) -> str:
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        try:
            completion = client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
            )
            logger.debug("OpenAI API call successful.")
            return completion.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return "Error during API call."


    @kernel_function(
        name="recommend_changes",
        description="Recommend mitigations based on detected defects"
    )
    async def recommend_changes(self, detected_defects) -> str:
        """
        Recommends changes to mitigate risks identified in the detected defects.

        Args:
            detected_defects (list): List of defect records with risk details.

        Returns:
            dict: JSON array with process_id, defect_summary, and recommended_change.
        """
        prompt = f"""
You are a risk and compliance advisor.

You will be provided with:
- Risk data (including _CASE_KEY, _INVOICE_KEY, VENDOR_CODE, PAYMENT_METHOD, RISK_TEXT, AGENT_IDENTIFIED_RISK,AGENT_COMMNET ).
- A set of business rules for how to address or mitigate such risks.

Your Task:
1. Based on the RISK_TEXT/AGENT_IDENTIFIED_RISK/AGENT_COMMNET select the appropriate rule from recommendation_rules to make a change recommendation.
2. Recommend an actionable resolution with point wise steps(AGENT_RECOMMENDED_CHANGE) to handle the risk.

Rules for Recommendation:
{json.dumps(self.rules, indent=2)}

Detected Defects:
{json.dumps(detected_defects, indent=2)}

Instructions:
- Do not mention rule numbers or titles.
- Keep recommendations concise and practical.
- Return the updated list as a valid JSON array.
"""
        
        detected_changes_json = self.call_openai(prompt=prompt)
        return detected_changes_json
