# RecommendChangesAgent README
 
## 1. Overview
The **RecommendChangesAgent** analyzes detected defects and recommends changes or actions to mitigate or resolve the issues. It leverages predefined business rules to generate actionable recommendations.
 
## 2. Features
- Analyzes detected defects to recommend changes.
- Utilizes a set of recommendation rules defined in a YAML file.
- Returns results as a structured JSON list containing recommended changes.
 
## 3. Dependencies
 
This agent depends on the following files:
- **Configuration File:** `RecommendDefectChangesAgent/config.yaml`
- **Plugin File:** `RecommendDefectChangesAgent/custom_plugins.py`
- **Rules File:** `../sample/recommendation_rules.yaml`
- **Agent file:** `../agent2_uc2.py`
 
### Environment Variables
 
Create a `.env` file at `RecommendDefectChangesAgent/.env` with the following required values:
 
```plaintext
TA_API_KEY="Use your KEY here"
TA_SERVICE_CONFIG=RecommendDefectChangesAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=misc/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
```
 
## 4. How the Agent Works
 
The agent operates through the following workflow:
 
1. **Configuration Loading:**  
   Loads configuration from `RecommendDefectChangesAgent/config.yaml`, which defines its structure, input types, and processing agents.
 
2. **Plugin Initialization:**  
   Imports `custom_plugins.py`, where the `RecommendDefectChangesAgent` class is defined. This class initializes necessary parameters and loads recommendation rules from the rules file.
 
3. **Defect Analysis:**  
   When detected defects are received, the `recommend_changes` function is called within the `RecommendDefectChangesAgent`. This function:
   - Analyzes the defect data based on the recommendation rules.
   - Constructs a prompt for the Azure OpenAI model to generate recommendations.
   - Calls the OpenAI API with the constructed prompt.
 
4. **Result Generation:**  
   Returns the recommendation result in a structured JSON format, including the recommended changes for each defect.
 
5. **Logging:**  
   A log file is created in the `/sample/` directory for each run, named with the format `agent_log_YYYY-MM-DD_HH-MM-SS` (e.g., `agent_log_2025-05-29_12-43-57`).
 
 
## 5. Usage
 
To run the agent, follow these steps:
 
1. Navigate to the main folder.
2. Activate the virtual environment by running the command:
  ```bash
  .venv/scripts/activate
  ```
3. Run the agent:
   ```bash
   python agent2_uc2.py
   ```
 
## 6. Sample Output
 
Here is a sample output from the last successful run of the RecommendChangesAgent:
 
```json
[
  {
    "_CASE_KEY": "1109330510946374720240010208",
    "_INVOICE_KEY": "110933051094637472024001",
    "VENDOR_CODE": "0003097722",
    "PAYMENT_METHOD": "M",
    "RISK_TEXT": "Payment Method on the Invoice and Vendor Master Data does not match.",
    "AGENT_IDENTIFIED_RISK": "The payment method has changed, indicating a potential risk of payment processing discrepancies.",
    "AGENT_COMMNET": "Invoice 35765 is impacted due to a change in payment method. The previous payment method was not provided, and the new payment method is M. This change may lead to discrepancies in payment processing.",
    "AGENT_RECOMMENDED_CHANGE": [
      "Log in to SAP: Open SAP Netweaver Portal and log in with your credentials. Select COMET Launchpad & ECC.",
      "Navigate to Financial Accounting - Document - Change: Use the transaction code FB02 (Change Accounts Payable Document).",
      "Enter Document Number: Input the document number, company code, and fiscal year you wish to update and press Enter.",
      "Select the Vendor Line Item: Double Click on Vendor Line. Navigate to Additional Data and Payment Terms where you can update Payment Terms.",
      "Update Payment Terms: Select the new Payment Terms.",
      "Save Changes: Click on the save button to update the Payment Terms."
    ]
  }
]
```
 
## 7. Unit Testing
 
To run the unit tests:
 
1. Navigate to the `RecommendDefectChangesAgent` folder.

2. Execute the following command in the terminal:
   ```bash
   python -m unittest -v .\tests\test_custom_plugins.py
   ```
 
## 8. API Access
 
Once running, access the Swagger documentation at:  
[http://localhost:8110/RecommendDefectChangesAgent/0.1/docs](http://localhost:8110/RecommendDefectChangesAgent/0.1/docs)