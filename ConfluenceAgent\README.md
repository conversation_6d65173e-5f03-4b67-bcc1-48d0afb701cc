# Confluence Agent

This agent is setup to run a query against confluence and find results based
on if the title or text of a confluence item contains the text value being used
in the query. For example "pie" it will look for all titles or texts that contain
"pie".

## Required Environment variables for local deployment

Please note there are two assumptions for this agent:

- your confluence instance has a tool like Kong, Apigee, Azure API portal,
    etc, sitting between your agent and confluence
- second you confluence still allows username and password authentication

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=ConfluenceAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_CONFLUENCE_API_KEY= Your Confluence Key
TA_API_GATEWAY_KEY_NAME=<The name of the key value for the header>
TA_BASE_CONFLUENCE_URL=<the base url for confluence api wrapped by you api gateway tool>
TA_UI_CONFLUENCE_URL=<the base url for your confluence ui(assumes custom base url for confluence)>
TA_USERNAME=<username for a nonperson account to login to confluence>
TA_PASSWORD=<password for logging into a non person account to confluence>
```

## Required Environment variables for platform deployment

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=ConfluenceAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CONFLUENCE_API_KEY= Your Confluence Key
TA_API_GATEWAY_KEY_NAME=<The name of the key value for the header>
TA_BASE_CONFLUENCE_URL=<the base url for confluence api wrapped by you api gateway tool>
TA_UI_CONFLUENCE_URL=<the base url for your confluence ui(assumes custom base url for confluence)>
TA_USERNAME=<username for a nonperson account to login to confluence>
TA_PASSWORD=<password for logging into a non person account to confluence>