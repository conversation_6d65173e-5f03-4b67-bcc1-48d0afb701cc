# Technical Specification for Agent Development

This document provides the technical specifications for README files accompanying agent configuration examples within the Teal Agents Framework.

## 1. Structure and Content

Each agent configuration example should include a `README.md` file with the following sections:

### 1.1. Overview

* **Agent Name and Version:** The `service_name` and `version` of the agent. Fill this with the agent's identifier and its semantic version (e.g., `ChatBot`, `0.1`).
* **Concise Description:** A brief summary of the agent's functionality, mirroring the `description` field in the YAML configuration. Describe what the agent is designed to do in a sentence or two.
* **Key Features:** A bulleted list highlighting the main technical capabilities or concepts demonstrated by this agent configuration (e.g., sequential task execution, chat-only interaction, utilization of custom input/output types, integration of local/remote plugins, processing of multi-modal input). Clearly indicate if the agent is `Sequential` or `Chat` kind.
* **Environment Variables:** Briefly list the key environment variables that are relevant to running this specific agent configuration. For each variable, provide a very short description of its purpose. More detailed explanations will be in the "Environment Setup" section. (e.g., `TA_API_KEY`: API key for LLM access; `TA_SERVICE_CONFIG`: Path to the agent config file).

### 1.2. Configuration Breakdown

This section details the elements of the associated YAML configuration file. The specific elements will vary based on the `kind` of the agent.

#### 1.2.1. `Chat` Kind

* **YAML Snippet:** The relevant portion (or the entire) YAML configuration file for a `Chat` agent.

    ```yaml
    apiVersion: skagents/v1
    kind: Chat
    description: >
      A simple chat agent
    service_name: ChatBot
    version: 0.1
    input_type: BaseInput
    spec:
      agent:
        name: default
        role: Default Agent
        model: gpt-4o-mini
        system_prompt: >
          You are a helpful assistant.
    ```

    or with plugins:

    ```yaml
    apiVersion: skagents/v1
    kind: Chat
    description: >
      A weather chat agent
    service_name: WeatherBot
    version: 0.1
    input_type: BaseInputWithUserContext
    spec:
      agent:
        name: default
        role: Default Agent
        model: gpt-4o
        system_prompt: >
          You are a helpful assistant.
        plugins:
        - WeatherPlugin
    ```

## Element-wise Specification for `Chat` Agent

This section provides a detailed explanation of each key element in the YAML configuration for a `Chat` agent.
| **Key Element**            | **Description**                                                                 | **Expected Values**                                   | **Notes**                                                                                     |
|----------------------------|---------------------------------------------------------------------------------|------------------------------------------------------|-----------------------------------------------------------------------------------------------|
| `apiVersion`               | The API version of the SK Agents Framework.                                    | `skagents/v1`                                       | Should always be set to `skagents/v1`.                                                       |
| `kind`                     | Defines the agent's execution model.                                           | `Chat`                                              | Indicates that this agent processes chat history directly without explicit sequential tasks.  |
| `description`              | A human-readable string describing the agent's purpose.                        | Any concise summary                                  | Fill with a brief description of what the chat agent does.                                   |
| `service_name`             | The unique name of the agent service.                                          | Any valid string                                     | Combined with `version` to form API endpoints (e.g., `/YourChatBot/YourVersion`).           |
| `version`                  | The semantic version of the agent service.                                     | Semantic version (e.g., `0.1`, `1.0`)               | Fill with the agent's version number.                                                        |
| `input_type`               | Specifies the expected format of the input payload.                            | Common types (e.g., `BaseInput`, `BaseInputWithUserContext`, `BaseMultiModalInput`) | Fill with the name of the input type class.                                                  |
| `spec.agent.name`         | A unique identifier for this agent within the configuration.                   | Any valid string                                     | Fill with a descriptive name for the agent.                                                 |
| `spec.agent.role`         | A description of the agent's role or persona.                                 | Any valid string                                     | Fill with the agent's intended role (e.g., `Helpful Assistant`, `Weather Information Provider`). |
| `spec.agent.model`        | The name of the Language Model (LLM) to be used by this agent.                | Model identifiers (e.g., `gpt-4o-mini`, `gpt-4o`)   | Fill with the desired LLM model identifier.                                                 |
| `spec.agent.system_prompt` | Initial instructions given to the LLM to guide its behavior in the chat context. | Multi-line string                                    | Fill with the system prompt appropriate for the agent's role.                               |
| `spec.agent.plugins`      | (Optional) List of local/custom plugin class names that this chat agent can use. | List of class names                                  | Must be defined in a Python module specified by `TA_PLUGIN_MODULE`.                          |
| `spec.agent.remote_plugins`| (Optional) List of names of remote plugins that this chat agent can use.      | List of plugin names                                 | Names must correspond to entries in the remote plugin catalog specified by `TA_REMOTE_PLUGIN_PATH`. |


#### 1.2.2. `Sequential` Kind

* **YAML Snippet:** The relevant portion (or the entire) YAML configuration file for a `Sequential` agent.

    ```yaml
    apiVersion: skagents/v1
    kind: Sequential
    description: >
      A simple chat agent
    service_name: ChatBot
    version: 0.1
    input_type: BaseInput
    spec:
      agents:
        - name: default
          role: Default Agent
          model: gpt-4o
          system_prompt: >
            You are a helpful assistant.
      tasks:
        - name: action_task
          task_no: 1
          description: Chat with user
          instructions: >
            Work with the user to assist them in whatever they need.
          agent: default
    ```

## Element-wise Specification for `Sequential` Agent

This section provides a detailed explanation of each key element in the YAML configuration for a `Sequential` agent.

| Key Element                     | Description                                                                 | Expected Values                                                                 | Notes                                                                                     |
|----------------------------------|-----------------------------------------------------------------------------|----------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|
| `apiVersion`                    | The API version of the SK Agents Framework.                                | `skagents/v1`                                                                    | Should always be set to `skagents/v1`.                                                  |
| `kind`                          | Defines the agent's execution model.                                       | `Chat`                                                                           | Indicates that this agent processes chat history directly without explicit sequential tasks. |
| `description`                   | A human-readable string describing the agent's purpose.                    | Any concise summary                                                              | Fill with a brief description of what the chat agent does.                             |
| `service_name`                  | The unique name of the agent service.                                      | Any valid string                                                                 | Combined with version to form API endpoints (e.g., `/YourChatBot/YourVersion`).         |
| `version`                       | The semantic version of the agent service.                                 | Semantic version (e.g., `0.1`, `1.0`)                                          | Fill with the agent's version number.                                                  |
| `input_type`                    | Specifies the expected format of the input payload.                        | Common types (e.g., `BaseInput`, `BaseInputWithUserContext`, `BaseMultiModalInput`) | Fill with the name of the input type class.                                            |
| `spec.agent.name`               | A unique identifier for this agent within the configuration.               | Any valid string                                                                 | Fill with a descriptive name for the agent.                                           |
| `spec.agent.role`               | A description of the agent's role or persona.                             | Any valid string                                                                 | Fill with the agent's intended role (e.g., Helpful Assistant, Weather Information Provider). |
| `spec.agent.model`              | The name of the Language Model (LLM) to be used by this agent.            | Model identifiers (e.g., `gpt-4o-mini`, `gpt-4o`)                               | Fill with the desired LLM model identifier.                                            |
| `spec.agent.system_prompt`       | Initial instructions given to the LLM to guide its behavior in the chat context. | Multi-line string                                                                | Fill with the system prompt appropriate for the agent's role.                          |
| `spec.tasks`                    | A list of tasks to be executed by the agent.                              | List of dictionaries                                                             | Each task is defined in the following sub-elements.                                    |
| `spec.tasks.name`               | The name of the task.                                                     | Any valid string                                                                 | Fill with a descriptive name for the task.                                             |
| `spec.tasks.task_no`            | The order in which the task should be executed.                           | Integer (1, 2, 3, ...)                                                           | Must be unique within the tasks list.                                                  |
| `spec.tasks.description`         | A description of the task.                                               | Any concise summary                                                              | Fill with a brief description of what the task entails.                                |
| `spec.tasks.instructions`        | Instructions for the task.                                               | Multi-line string                                                                | Fill with specific instructions for the task.                                          |
| `spec.tasks.agent`              | The agent to use for the task (must match agent defined in the agents section). | Any valid string                                                                 | Should correspond to the name of an agent defined in the agents section.               |


#### Sequential Tasks (Applicable to `Sequential` Kind Only)

* **YAML Snippet:** The relevant portion of the YAML configuration showing the `tasks` list with multiple entries.

    ```yaml
    tasks:
      - name: action_task
        task_no: 1
        description: Add two numbers
        instructions: >
          Add the following two numbers together
          {{number_1}} {{number_2}}
        agent: default
      - name: follow_on_task
        task_no: 2
        description: Perform a final operation
        instructions: >
          Multiply the result of the previous answer by {{number_3}} and then add
          10 to it.
          Previous operation:
          {{_action_task}}
        agent: default
    ```

* **Technical Details:** The `{{_action_task}}` variable is used to reference the output of the `action_task` task. This is a special variable that is automatically created by the Teal Agents Framework when a task is completed. The variable name is simply the
name of the task, preceded by an underscore.

### 1.3. Custom Input/Output Types

If the agent configuration utilizes custom input or output types:

* **Code Snippet:** The Python code defining the custom type(s). Include the full Python class definition(s).

    ```python
    # custom_types.py
    from semantic_kernel.kernel_pydantic import KernelBaseModel

    class NumbersInput(KernelBaseModel):
        number_1: int
        number_2: int

    class AddOutput(KernelBaseModel):
        result: int
    ```

* **Type Specification:** For each custom type, detail the purpose of the class and the type and description of each field it contains. Reference the corresponding `input_type` or `output_type` in the main configuration. For `embedded_image`, specify the expected fields (`format`, `content` or `data`).

### 1.4. Plugins

If the agent configuration utilizes plugins:

#### 1.4.1. Local/Custom Plugins

* **Code Snippet:** A relevant snippet from the custom plugin code, highlighting the inheritance from `BasePlugin` and the `@kernel_function` decorator.

    ```python
    # custom_plugins.py
    from ska_types.models import BasePlugin
    from semantic_kernel.functions import kernel_function

    class WeatherPlugin(BasePlugin):
        @kernel_function(
            description="Retrieve low and high temperatures for the day for a given location"
        )
        def get_temperature(self, lat: float, lng: float, timezone: str) -> dict:
            # ... plugin logic ...
            pass
    ```

| Feature                  | Description                                                                                                                                                                                             |
| :----------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Inheritance** | Custom plugins **must** inherit from the `BasePlugin` class provided by the Semantic Kernel framework. This provides foundational functionalities and attributes for your plugin.                       |
| `@kernel_function`       | This decorator is used to mark methods within your custom plugin class as **callable functions** that the agent can discover and execute. The `description` parameter within the decorator provides a natural language explanation of the function's purpose. |
| `authorization`        | This attribute, available in `BasePlugin`, can be used to manage authorization or authentication mechanisms required by your plugin's functionalities.                                                    |
| `extra_data_collector` | This attribute, also part of `BasePlugin`, allows you to implement custom logic for collecting additional data or context relevant to your plugin's operations.                                            |

#### 1.4.2. Remote Plugins

* **YAML Snippet:** The relevant part of the `remote-plugin-catalog.yaml` file.

    ```yaml
    remote_plugins:
      - plugin_name: api_weather
        openapi_json_path: ./demos/04_remote_plugins/openapi_weather.json
        server_url: [https://api.open-meteo.com](https://api.open-meteo.com)
    ```

* **Technical Details:** Remote plugins are defined in a separate catalog file. For each remote plugin used, detail the meaning of `plugin_name` (as referenced in the agent config), `openapi_json_path` (path to the OpenAPI specification), and `server_url` (base URL of the API).

| Parameter           | Description                                                                 |
| :------------------ | :-------------------------------------------------------------------------- |
| `plugin_name`       | The unique identifier for the remote plugin, as referenced in the agent's configuration. |
| `openapi_json_path` | The file path to the OpenAPI specification (in JSON format) that describes the remote plugin's API. |
| `server_url`        | The base URL(s) of the remote API server.                                 |


### 1.5. Multi-Modal Input

*Coming soon*

### 1.6. Environment Variables

This section lists the required environment variables along with a detailed explanation of what each variable controls and the values that should be provided.

| **Environment Variable**   | **Description**                                                                 | **Expected Values**                                   | **Required** |
|----------------------------|---------------------------------------------------------------------------------|------------------------------------------------------|--------------|
| `TA_API_KEY`               | Your API key for accessing apiGPTeal services.                                   | A valid API key string                               | Yes          |
| `TA_BASE_URL`              | Base URL for the API service.                                                   | The valid apiGPTeal URL for TA_API_KEY (e.g.`https://iapi-test.merck.com/gpt/libsupport`)        | Yes          |
| `TA_SERVICE_CONFIG`        | Path to the agent configuration YAML file.                                     | Path to the YAML file (e.g., `./config.yaml`)       | Yes          |
| `TA_TYPES_MODULE`          | Path to the Python module defining custom input/output types.                  | Path to the Python module (e.g., `my_module.types`)  | No           |
| `TA_PLUGIN_MODULE`         | Path to the Python module containing custom plugin classes.                    | Path to the Python module (e.g., `my_module.plugins`) | No           |
| `TA_REMOTE_PLUGIN_PATH`    | Path to the remote plugin catalog YAML file.                                   | Path to the YAML file (e.g., `./remote_plugins.yaml`) | No           |



## 2. Payloads and Input/Output Objects 

### 2.1. Usage Examples

Provide technical examples of how to interact with the configured agent. For up-to-date technical information on your current `BaseInput` or `BaseMultiModalInput` with your agent running, check the agent redoc at: [http://localhost:8110/<YourAgentName>/0.1/redoc](http://localhost:8110/<YourAgentName>/0.1/redoc).

### Request Payload (for REST endpoints)

Example JSON payloads demonstrating the structure expected by the agent based on its `input_type`. 

- For `Sequential` agents, show payloads that align with the input requirements of the first task.
- For `Chat` agents, show the structure of the `chat_history` (for `BaseInput`, `BaseMultiModalInput`) or the fields of other input types like `BaseInputWithUserContext`.
- For multi-modal input, include examples for both `BaseMultiModalInput` and custom types with `embedded_image`, including the base64-encoded image data.

### Expected Response (for REST endpoints)

Example JSON responses, detailing the structure of the `token_usage`, `extra_data`, `output_raw`, and `output_pydantic` fields (if applicable based on the `output_type`). 

- For `Sequential` agents, show the response after all tasks have been executed.
- For `Chat` agents, show a typical chat response.

### SSE Request Payload

*Coming soon*

### SSE Expected Response

*Coming soon*

### Streaming Interaction

Describe the endpoint for streaming and the format of the streamed output (typically raw text chunks, followed by an optional `extra_data` JSON object at the end).

---
### 2.2.1 BaseInput Object
The `BaseInput` object represents the history of a chat interaction between an automated assistant and a human.

### chat_history
- **Type**: `array<object> | null`
- **Description**: An array of chat interactions. Each interaction is represented as an object.

#### Interaction Object
Each interaction in the chat history is represented as follows:

- **role**: 
  - **Type**: `string`
  - **Enum**: 
    - `"user"`: Indicates the message was sent by the user (requestor).
    - `"assistant"`: Indicates the message was sent by the assistant (responder).
  
- **content**: 
  - **Type**: `string | null`
  - **Description**: The content of the message sent in the interaction.

### Example
```json
{
  "chat_history": [
    {
      "role": "user",
      "content": "Hello, how can I help you?"
    },
    {
      "role": "assistant",
      "content": "I am here to assist you with your queries."
    }
  ]
}
```

### 2.2.2 BaseInputWithUserContext Object
The `BaseInputWithUserContext` object represents the history of a chat interaction between an automated assistant and a human, along with context about the user.

### chat_history
- **Type**: `array<object> | null`
- **Description**: An array of chat interactions. Each interaction is represented as an object.

#### Interaction Object
Each interaction in the chat history is represented as follows:

- **role**: 
  - **Type**: `string`
  - **Enum**: 
    - `"user"`: Indicates the message was sent by the user (requestor).
    - `"assistant"`: Indicates the message was sent by the assistant (responder).
  
- **content**: 
  - **Type**: `string | null`
  - **Description**: The content of the message sent in the interaction.

### user_context
- **Type**: `object | null`
- **Description**: Additional context about the user.

#### Additional Properties
- **Type**: `string | null`
- **Description**: Any additional properties related to the user context.

### Example
```json
{
  "chat_history": [
    {
      "role": "user",
      "content": "Hello, how can I help you?"
    },
    {
      "role": "assistant",
      "content": "I am here to assist you with your queries."
    }
  ],
  "user_context": {
    "location": "New York",
    "preferences": {
      "language": "English"
    }
  }
}
```

### 2.2.3 BaseMultiModalInput Object
The `BaseMultiModalInput` object represents a chat interaction that can include multiple types of content (e.g., text, images).

### chat_history
- **Type**: `array<object> | null`
- **Description**: An array of chat interactions. Each interaction is represented as an object.

#### Interaction Object
Each interaction in the chat history is represented as follows:

- **role**: 
  - **Type**: `string`
  - **Enum**: 
    - `"user"`: Indicates the message was sent by the user (requestor).
    - `"assistant"`: Indicates the message was sent by the assistant (responder).

- **items**: 
  - **Type**: `array<object>`
  - **Description**: An array of content items associated with the interaction.

##### Content Item Object
Each content item in the interaction is represented as follows:

- **content_type**: 
  - **Type**: `string`
  - **Enum**: 
    - `"image"`: Indicates the content is an image.
    - `"text"`: Indicates the content is text.

- **content**: 
  - **Type**: `string | null`
  - **Description**: The actual content of the item.

### Example
```json
{
  "chat_history": [
    {
      "role": "user",
      "items": [
        {
          "content_type": "text",
          "content": "Can you show me a picture of a cat?"
        }
      ]
    },
    {
      "role": "assistant",
      "items": [
        {
          "content_type": "image",
          "content": "https://example.com/cat.jpg"
        }
      ]
    }
  ]
}
```

### 2.2.4 InvokeResponse_Any Object
The `InvokeResponse_Any` object represents the response from an invocation, including token usage and output data.

### token_usage
- **Type**: `object`
- **Description**: Contains information about token usage during the invocation.

#### Token Usage Object
The token usage is represented as follows:

- **completion_tokens**: 
  - **Type**: `integer`
  - **Description**: The number of tokens used for the completion.

- **prompt_tokens**: 
  - **Type**: `integer`
  - **Description**: The number of tokens used for the prompt.

- **total_tokens**: 
  - **Type**: `integer`
  - **Description**: The total number of tokens used (completion + prompt).

### extra_data
- **Type**: `object | null`
- **Description**: Additional data related to the invocation response.

#### Extra Data Object
The extra data can include various properties and is represented as follows:

- **Type**: `object`
- **Description**: Contains any extra data related to the response.

### output_raw
- **Type**: `string | null`
- **Description**: The raw output of the invocation, which may be null.

### output_pydantic
- **Type**: `any`
- **Description**: The output in Pydantic format, allowing for additional properties.

### Example
```json
{
  "token_usage": {
    "completion_tokens": 50,
    "prompt_tokens": 30,
    "total_tokens": 80
  },
  "extra_data": {
    "some_property": "value"
  },
  "output_raw": "This is the raw output.",
  "output_pydantic": {
    "additional_property": "value"
  }
}
```


## 3. Consistency and Best Practices

* **Clarity and Conciseness:** Use precise technical language.
* **Code Formatting:** Ensure all code snippets (YAML and Python) are correctly formatted and easy to read.
* **Accuracy:** Double-check that all information, especially configuration details and code examples, is accurate.
* **Completeness:** Ensure all relevant technical aspects of the configuration are explained.
* **Linkage:** When referring to other concepts or documentation, provide clear links if possible (within the broader documentation structure).
* **Tone:** Maintain a neutral and informative technical tone.