apiVersion: skagents/v1
kind: Sequential
description: >
  A Recommend Defect Change Agent
service_name: RecommendDefectChangesAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-08-06
      system_prompt: >
        You are a risk assessment and remediation agent.
        Your task is to analyze the detected defects and recommend changes or actions that can mitigate or resolve the issues.
        Ensure that the output is structured as a valid JSON list containing the recommended changes.
      plugins:
        - RecommendDefectChangesAgent
  tasks:
    - name: action_task
      task_no: 1
      description: Recommend changes for identified defects.
      instructions: >
        1. Call the 'recommend_changes' function.
        2. The function will automatically load rules from the specified YAML file.
        3. The response format should be as per "celonis_field_classification_rules" from the YAML rules file.
        4. AGENT_RECOMMENDED_CHANGE should be filled by using the recommendation_rules in point wise with all the steps and rest of the field should be directly mapped from the input.
        5. Ensure that all detected defects are processed to generate recommendations.
        6. Return the recommended changes as a JSON list.
        7. Do not include any extra text or explanation in the JSON output.
      agent: default  # Ensure this matches the agent name defined above
  file_paths:
    RECOMMENDATION_RULES_FILE: "../sample/recommendation_rules.yaml"
