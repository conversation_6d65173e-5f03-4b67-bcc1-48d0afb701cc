import os
import json
import re
import pandas as pd
import yaml
from dotenv import load_dotenv
from typing import Dict, List, Any
from semantic_kernel import Kern<PERSON>
from semantic_kernel.functions import kernel_function
from openai import AzureOpenAI
from sk_agents.ska_types import BasePlugin
from logger.agent_loggers import Logger 
mapping_logger=Logger()
from aws_connector import readProductDetailsFromS3
# Load environment variables from .env file
load_dotenv()

def load_config(config_path: str) -> Dict:
    try:
        with open(config_path, "r") as file:
            config= yaml.safe_load(file)
            mapping_logger.debug(f"Loaded configuration: {config}")
            return config
    except FileNotFoundError:
        mapping_logger.error(f"Configuration file not found at {config_path}")
        return {}
    except Exception as e:
        mapping_logger.error(f"Error loading configuration: {e}")
        return {}


def load_mapping_rules(file_path: str) -> Dict:
    try:
        with open(file_path, "r") as file:
            data = yaml.safe_load(file)
            mapping_logger.debug(f"Loaded mapping rules: {data}")
        return data.get("sap_classification_rules", {})
    except FileNotFoundError:
        mapping_logger.error(f"Rules file not found at {file_path}. No rules loaded.")
        return {}
    except Exception as e:
        mapping_logger.error(f"Error loading rules from {file_path}: {e}. No rules loaded.")
        return {}


class OrderMappingPlugin(BasePlugin):
    def __init__(self, kernel: Kernel, rules_path: str):
        self.kernel = kernel
        self.sample_dir = self._find_sample_dir()

        # Set file paths
        self.rules_file = os.path.join(self.sample_dir, "sap_rules.yaml")
        # Load rules from the specified path
        self.rules = load_mapping_rules(self.rules_file)


    def _find_sample_dir(self) -> str:
        """Find the sample directory using multiple possible paths"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths = [
            os.path.join(os.path.dirname(current_dir), "sample"),  # ../sample
            os.path.join(current_dir, "sample"),                   # ./sample
            os.path.join(os.path.dirname(os.path.dirname(current_dir)), "sample"),  # ../../sample
        ]
        for path in possible_paths:
            if os.path.exists(path):
                mapping_logger.info(f"Found sample directory at: {path}")
                return path

    @staticmethod
    def call_openai(prompt: str) -> str:
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        try:
            completion = client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
            )
            mapping_logger.debug("OpenAI API call successful.")
            return completion.choices[0].message.content.strip()
        except Exception as e:
            mapping_logger.error(f"Error calling OpenAI API: {e}")
            return "Error during API call."
        
    @kernel_function(
        name="map_order_fields",
        description="Maps email order data to SAP format using defined rules."
    )
    def map_order_fields(self, data_json: str) -> str:
        """
        Maps input order JSON to SAP format using defined rules.

        Args:
            data_json: JSON string with order data

        Returns:
            str: Mapped SAP-compliant JSON string or error message.
        """
        mapping_logger.info("Starting JSON mapping process.")

        # Prepare the prompt for the LLM
        rules_string = ", ".join(
            f"{source} -> {target}" for rule in self.rules for source, target in rule.items()
        )
        prompt = (
            "You are an order mapping agent.\n"
            f"Using the following mapping rules: {rules_string}\n\n"
            f"Map the following order data to SAP format:\n{data_json}\n\n"
            "Note: Use semantic analysis to map all 'SAP fields' from the rules file against the order data as the fields names in 'order_data' might differ to the mapping rule fields. "
            "Ensure that all fields are at the same level and not nested within the array. "
            "Return the mapped data as a JSON string. "
            "DO NOT provide any explanation, return only the mapped json data."
        )

        # Log the input prompt
        mapping_logger.debug(f"Input prompt for OpenAI: {prompt}")

        # Call the OpenAI model with the prompt
        mapped_json = self.call_openai(prompt)

        mapping_logger.info(f"Mapped JSON response: {mapped_json}")
        df = readProductDetailsFromS3()
        print(df)
        match = re.search(r'"material_id":\s*"([^"]+)"', mapped_json)
        if match:
            material_id = int(match.group(1))
            mapping_logger.info(f"Material ID: {material_id}")
        else:
            mapping_logger.info("MATERIAL ID NOT FOUND")
            material_id = 0
        
        required_columns = ['Material', 'Material_Description']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            mapping_logger.info(f"Missing columns in the DataFrame: {missing_columns}")
            return mapped_json

        material_data = df[['Material', 'Material_Description']].to_dict(orient='records')

        json_match = re.search(r'\{.*\}', mapped_json, re.DOTALL)
        if json_match:
            mapped_json = json.loads(json_match.group(0))
        else:
            mapping_logger.error("Invalid JSON format in mapped_json")
            return mapped_json

        material_id_flag = False
        for item in material_data:
            if item.get('Material') == material_id:
                material_description = item.get('Material_Description')
                material_id_flag = True
                break

        if material_id_flag:
            mapping_logger.info("Got Material Id..")
            product_name = mapped_json.get("product_name")
            if product_name == material_description:
                return json.dumps(mapped_json)
            else:
                mapped_json["product_name"] = material_description
                return json.dumps(mapped_json)
        else:
            mapped_json["material_id"] = "Material Id not found!"
            return json.dumps(mapped_json)