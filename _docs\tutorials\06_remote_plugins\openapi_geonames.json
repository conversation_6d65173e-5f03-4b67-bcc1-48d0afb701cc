{"openapi": "3.0.3", "info": {"title": "Get Lat/Lng/Time Zone for Location", "description": "Get latitude, longitude, and time zone for a given location", "version": "1.0.0"}, "servers": [{"url": "http://api.geonames.org"}], "paths": {"/searchJSON": {"get": {"summary": "Get Lat/Lng/Time Zone for Location", "description": "Get latitude, longitude, and time zone for a given location", "operationId": "GetLocationDetail", "parameters": [{"in": "query", "name": "formatted", "schema": {"type": "boolean", "default": true}, "description": "Format the output", "required": true}, {"in": "query", "name": "q", "schema": {"type": "string"}, "description": "A location search string", "required": true}, {"in": "query", "name": "maxRows", "schema": {"type": "integer", "default": 1}, "description": "The number of results to return", "required": true}, {"in": "query", "name": "lang", "schema": {"type": "string", "default": "en"}, "description": "Language of the result", "required": true}, {"in": "query", "name": "username", "schema": {"type": "string", "default": "tealagents"}, "description": "Requestor username", "required": true}, {"in": "query", "name": "style", "schema": {"type": "string", "default": "full"}, "description": "Output style (defaults to full)", "required": true}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBody"}}}}}}}}, "components": {"schemas": {"timezone": {"description": "Timezone details for the given location", "properties": {"gmtOffset": {"type": "number", "title": "gmtOffset", "description": "Timezone offset from GMT"}, "timeZoneId": {"type": "string", "title": "timeZoneId", "description": "The ID of the time zone"}, "dstOffset": {"type": "number", "title": "dstOffset", "description": "The time zone offset during daylight savings time"}}}, "bbox": {"description": "Lat/Lng bounding box for location", "properties": {"east": {"type": "number", "title": "east", "description": "East longitude of bounding box"}, "south": {"type": "number", "title": "south", "description": "South latitude of bounding box"}, "north": {"type": "number", "title": "north", "description": "North latitude of bounding box"}, "west": {"type": "number", "title": "west", "description": "West longidue of bounding box"}, "accuracy": {"type": "number", "title": "accuracy", "description": "Accuracy of bounding box"}}}, "alternateName": {"description": "An alternate name for the location", "properties": {"name": {"type": "string", "title": "name", "description": "The alternate name for the location"}, "lang": {"type": "string", "title": "lang", "description": "The language of the alternate name"}}}, "geoname": {"description": "Geographical data about the location", "properties": {"timezone": {"$ref": "#/components/schemas/timezone"}, "bbox": {"$ref": "#/components/schemas/bbox"}, "asciiName": {"type": "string", "title": "asciiName", "description": "The ASCII name of the location"}, "astergdem": {"type": "number"}, "countryId": {"type": "string"}, "fcl": {"type": "string"}, "srtm3": {"type": "number"}, "score": {"type": "number"}, "adminId2": {"type": "string"}, "adminId3": {"type": "string"}, "countryCode": {"type": "string"}, "adminId1": {"type": "string"}, "lat": {"type": "string", "title": "lat", "description": "The latitude of the location as a string (can be converted to a number)"}, "fcode": {"type": "string"}, "continentCode": {"type": "string"}, "elevation": {"type": "number"}, "adminCode2": {"type": "string"}, "adminCode3": {"type": "string"}, "adminCode1": {"type": "string"}, "lng": {"type": "string", "title": "lng", "description": "The longitude of the location as a string (can be converted to a number)"}, "geonameId": {"type": "number"}, "toponymName": {"type": "string"}, "population": {"type": "number"}, "adminName5": {"type": "string"}, "adminName4": {"type": "string"}, "adminName3": {"type": "string"}, "alternateNames": {"type": "array", "items": {"$ref": "#/components/schemas/alternateName"}}, "adminName2": {"type": "string"}, "name": {"type": "string", "title": "name", "description": "The name of the location"}, "fclName": {"type": "string"}, "countryName": {"type": "string", "description": "The country of the location"}, "fcodeName": {"type": "string"}, "adminName1": {"type": "string"}}}, "ResponseBody": {"properties": {"totalResultsCount": {"type": "number", "title": "totalResultsCount", "description": "The total number of results found for the given search string"}, "geonames": {"description": "Metadata about the daily structure", "type": "array", "items": {"$ref": "#/components/schemas/geoname"}}}}}}}