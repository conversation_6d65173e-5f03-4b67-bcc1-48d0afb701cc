# Streamlit Demo Application Requirements
# This file contains the dependencies needed to run the standalone Streamlit demo

# Core Streamlit framework
streamlit>=1.28.0

# Data processing and manipulation
pandas>=2.0.0
numpy>=1.24.0

# HTTP requests for API calls
requests>=2.31.0

# Email processing
email-validator>=2.0.0

# File handling and utilities
python-multipart>=0.0.6
pathlib2>=2.3.7

# Optional: For better email parsing (if needed)
# python-email>=0.6.3

# Optional: For Excel file support
openpyxl>=3.1.0
xlrd>=2.0.1

# Optional: For PDF processing (if needed in future)
# PyPDF2>=3.0.0
# pdfplumber>=0.9.0

# Development and testing (optional)
# pytest>=7.0.0
# pytest-streamlit>=0.1.0
