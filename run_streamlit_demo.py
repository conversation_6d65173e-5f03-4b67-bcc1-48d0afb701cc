#!/usr/bin/env python3
"""
Launcher script for the Agent Orchestration Streamlit Demo

This script helps users start the Streamlit demo application with proper
environment setup and dependency checking.
"""

import subprocess
import sys
import os
import requests
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 11):
        print("❌ Error: Python 3.11 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'streamlit',
        'pandas',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements_streamlit.txt"
            ])
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements_streamlit.txt")
            return False
    
    return True

def check_service_health():
    """Check if the orchestration service is running."""
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Orchestration service is running (port 8000)")
            return True
        else:
            print(f"⚠️ Orchestration service responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ Orchestration service is not running (port 8000)")
        return False

def check_agent_services():
    """Check if individual agent services are running."""
    agents = [
        ("EmailClassifyingAgent", 5000),
        ("ConfidenceScoreAgent", 6010),
        ("MappingAgent", 7000)
    ]
    
    all_running = True
    
    for agent_name, port in agents:
        try:
            response = requests.get(f"http://localhost:{port}/", timeout=3)
            if response.status_code == 200:
                print(f"✅ {agent_name} is running (port {port})")
            else:
                print(f"⚠️ {agent_name} responded with status {response.status_code} (port {port})")
                all_running = False
        except requests.exceptions.RequestException:
            print(f"❌ {agent_name} is not running (port {port})")
            all_running = False
    
    return all_running

def print_startup_instructions():
    """Print instructions for starting required services."""
    print("\n" + "="*60)
    print("🚀 STARTING REQUIRED SERVICES")
    print("="*60)
    print("\nTo use the demo, you need to start the following services:")
    print("\n1. Individual Agent Services:")
    print("   Terminal 1: python agent1_uc1.py  # EmailClassifyingAgent (port 5000)")
    print("   Terminal 2: python agent2_uc1.py  # ConfidenceScoreAgent (port 6010)")
    print("   Terminal 3: python agent3_uc1.py  # MappingAgent (port 7000)")
    print("\n2. Orchestration Service:")
    print("   Terminal 4: python main_run_uc1.py  # Orchestration (port 8000)")
    print("\n3. Then run this demo:")
    print("   Terminal 5: streamlit run streamlit_demo_app.py")
    print("\n" + "="*60)

def main():
    """Main launcher function."""
    print("🤖 Agent Orchestration Streamlit Demo Launcher")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check if demo files exist
    demo_file = Path("streamlit_demo_app.py")
    if not demo_file.exists():
        print("❌ Error: streamlit_demo_app.py not found")
        print("Please ensure you're running this script from the correct directory")
        sys.exit(1)
    
    print("\n🔍 Checking Service Status...")
    
    # Check services
    orchestration_running = check_service_health()
    agents_running = check_agent_services()
    
    if not orchestration_running or not agents_running:
        print("\n⚠️ Some services are not running.")
        print("The demo will still start, but you'll need to start the missing services.")
        print_startup_instructions()
        
        response = input("\nDo you want to continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Demo cancelled. Please start the required services first.")
            sys.exit(1)
    else:
        print("\n✅ All services are running!")
    
    # Start Streamlit
    print("\n🚀 Starting Streamlit Demo...")
    print("The demo will open in your default browser at: http://localhost:8501")
    print("\nPress Ctrl+C to stop the demo")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_demo_app.py"
        ])
    except KeyboardInterrupt:
        print("\n\n👋 Demo stopped. Thank you for using the Agent Orchestration Demo!")
    except Exception as e:
        print(f"\n❌ Error starting Streamlit: {e}")
        print("You can try running manually: streamlit run streamlit_demo_app.py")

if __name__ == "__main__":
    main()
