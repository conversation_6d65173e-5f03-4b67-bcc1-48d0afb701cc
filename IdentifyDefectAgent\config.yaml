apiVersion: skagents/v1
kind: Sequential
description: >
  A Celonis Defect Identification Agent
service_name: IdentifyDefectAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-08-06
      system_prompt: >
        You are an expert in identifying defects in Celonis data.
        Your task is to analyze the provided Celonis data and identify any defects based on the specified rules.
        For each record, check against the defect detection rules and classify any identified defects.
        Ensure that the output is structured as a valid JSON array containing only the identified defects.
      plugins:
        - IdentifyDefectPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Identify defects in Celonis data.
      instructions: >
        1. Call the 'identify_defects' function.
        2. Pass the received input data as the paramter 'celonis_data' to 'identify_defects' function.
        3. The function will automatically load rules from the specified YAML file.
        4. Input is celonis data.Ensure that all records in the Celonis data are checked against the defect detection rules.
        5. Return the identified defects as a JSON array.
        6. Do not include any extra text or explanation in the JSON output.
      agent: default
  file_paths:
    DEFECT_RULES_FILE: "../sample/defect_rules.yaml"
