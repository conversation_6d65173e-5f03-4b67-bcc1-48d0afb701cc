# Travel Agent

This agent is designed to use a rag based search api to respond and provide 
links to relevant travel topics passed to it. For example if the question 
"what is needed when traveling to china" then it should provide a list of potential
internal topics that may apply

## Environment Variables for local development

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=TravelAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_SEARCH_URL=<Your Search URL>
TA_SEARCH_API_KEY=<Your API Key >
TA_SEARCH_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Environment Variables for platform development

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=TravelAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_SEARCH_URL=<Your Search URL>
TA_SEARCH_API_KEY=<Your API Key >
TA_SEARCH_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Dedicated Hardware

- No additional dedicated hardware is needed for this agent currently