from typing import Any, Dict, List, Optional, Union
from fastapi import FastAPI, Body, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
import requests
import json
import uvicorn
from aws_connector import readS3
from logger.agent_loggers import Logger

main_use_case1_logger = Logger()

app = FastAPI(title="Agent Orchestration API")

@app.get("/")
async def root():
    """Root endpoint that redirects to documentation."""
    return {"message": "Welcome to Agent Orchestration API", 
            "documentation": "Visit /docs for the interactive API documentation"}

# Data models
class InputData(BaseModel):
    input_text: dict

class ResponseModel(BaseModel):
    agent1_response: Optional[Any]  
    agent2_response: Optional[Any]  
    agent3_response: Optional[Any]

def create_input_text(input_data: dict) -> dict:
    """Helper function to create input text for agents."""
    input_string = json.dumps(input_data)
    enclosed_json_string = f'"{input_string}"'
    return {
        "chat_history": [
            {
                "role": "user",
                "content": enclosed_json_string
            }
        ]
    }

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle validation errors."""
    main_use_case1_logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle all unhandled exceptions."""
    main_use_case1_logger.error(f"Unhandled error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred."}
    )

def call_agent(agent_number: int, url: str, input_data: dict) -> Any:
    """Generic function to call an agent and handle errors."""
    try:
        response = requests.post(url, json=input_data)
        response.raise_for_status()
        return extract_output_raw(response.text)
    except requests.exceptions.HTTPError as e:
        main_use_case1_logger.error(f"HTTP error calling Agent {agent_number}: {e}")
        raise HTTPException(status_code=e.response.status_code, detail=f"HTTP error with Agent {agent_number}: {e.response.text}")
    except requests.exceptions.RequestException as e:
        main_use_case1_logger.error(f"Error calling Agent {agent_number}: {e}")
        raise HTTPException(status_code=500, detail={"detail": f"Error processing request with Agent {agent_number}", "error": str(e)})

@app.post("/master/agent", response_model=ResponseModel)
def process(data: InputData):
    main_use_case1_logger.info("Received input data for processing.")
    
    classify_input = readS3()
    main_use_case1_logger.info(f"Data read from S3: {classify_input}")

    input_for_agent1 = create_input_text(classify_input)
    main_use_case1_logger.info(f"Agent 1 input: {input_for_agent1}")

    agent1_output = call_agent(1, "http://localhost:5000/EmailClassifyingAgent/0.1", input_for_agent1)
    main_use_case1_logger.info(f"Response from Agent 1: {agent1_output}")

    classification = agent1_output.get("classification")
    
    json_output = {
        "agent1_response": agent1_output,
        "agent2_response": None,
        "agent3_response": None
    }

    if classification == "order":
        main_use_case1_logger.info("Email classified as an order. Proceeding to Agent 2.")
        input_for_agent2 = create_input_text(agent1_output)
        main_use_case1_logger.info(f"Agent 2 input: {input_for_agent2}")

        agent2_output = call_agent(2, "http://localhost:6010/ConfidenceScoreAgent/0.1", input_for_agent2)
        main_use_case1_logger.info(f"Response from Agent 2: {agent2_output}")
        json_output["agent2_response"] = agent2_output
        average_confidence_score = extract_average_confidence_score(agent2_output)
        main_use_case1_logger.info(f"AVG SCORE: {average_confidence_score}")

        if int(average_confidence_score) > 85:
            main_use_case1_logger.info("average_confidence_score is greater than threshold. Proceeding to Agent 3.")
            input_for_agent3 = create_input_text(agent2_output)
            main_use_case1_logger.info(f"Agent 3 input: {input_for_agent3}")

            agent3_output = call_agent(3, "http://localhost:7000/MappingAgent/0.1", input_for_agent3)
            json_output["agent3_response"] = agent3_output
            main_use_case1_logger.info(f"Response from Agent 3: {agent3_output}")
        else:
            main_use_case1_logger.info("average_confidence_score is less than threshold, terminating the process")
    main_use_case1_logger.info(f"Final Output:\n{json.dumps(json_output, indent=2)}")
    return json_output

def extract_average_confidence_score(agent2_output):
    if isinstance(agent2_output, list):
        return agent2_output[0].get("average_confidence_score", 0)
    elif isinstance(agent2_output, dict):
        return agent2_output.get("average_confidence_score", 0)
    elif isinstance(agent2_output, str):
        agent2_output = json.loads(agent2_output)
        return agent2_output.get("average_confidence_score", 0)
    return 0

def extract_output_raw(json_string):
    data = json.loads(json_string)
    output_raw = data.get("output_raw")
    if output_raw:
        cleaned_output_raw = output_raw.strip("```json\n").strip("```").strip()
        return json.loads(cleaned_output_raw)
    return None

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)




# from typing import Any, Dict, List, Optional, Union
# from fastapi import FastAPI, Body, HTTPException
# from pydantic import BaseModel
# import requests
# import json
# import uvicorn
# from aws_connector import readS3
# from logger.agent_loggers import Logger

# main_use_case1_logger = Logger()

# app = FastAPI()

# # Data models
# class InputData(BaseModel):
#     input_text: dict

# class ResponseModel(BaseModel):
#     agent1_response: Optional[Any]  
#     agent2_response: Optional[Any]  
#     agent3_response: Optional[Any]

# def create_input_text(input_data: dict) -> dict:
#     """Helper function to create input text for agents."""
#     input_string = json.dumps(input_data)
#     enclosed_json_string = f'"{input_string}"'
#     return {
#         "chat_history": [
#             {
#                 "role": "user",
#                 "content": enclosed_json_string
#             }
#         ]
#     }

# # with condition for both agent2 and 3
# @app.post("/master/agent", response_model=ResponseModel)
# def process(data: InputData):
#     main_use_case1_logger.info("Received input data for processing.")
    
#     # Call the function
#     classify_input = readS3()
#     main_use_case1_logger.info(f"Data read from S3: {classify_input}")

#     input_for_agent1 = create_input_text(classify_input)
#     main_use_case1_logger.info(f"Agent 1 input: {input_for_agent1}")
#     try:
#         agent1_response = requests.post("http://localhost:5000/EmailClassifyingAgent/0.1", json=input_for_agent1)
#         agent1_response.raise_for_status()  # Raise an error for bad responses
#         agent1_output = extract_output_raw(agent1_response.text)
#         main_use_case1_logger.info(f"Response from Agent 1: {agent1_output}")
#     except requests.exceptions.RequestException as e:
#         main_use_case1_logger.error(f"Error calling Agent 1: {e}")
#         raise HTTPException(status_code=500, detail="Error processing request with Agent 1")

#     main_use_case1_logger.info(f"\n{type(agent1_output)}")
#     # Extract classification from Agent 1's response
#     if isinstance(agent1_output,str):
#         agent1_output = json.loads(agent1_output)
#     classification = agent1_output.get("classification")
#     if classification == "order":
#         main_use_case1_logger.info("Email classified as an order. Proceeding to Agent 2.")
#         input_for_agent2 = create_input_text(agent1_output)
#         main_use_case1_logger.info(f"Agent 2 input: {input_for_agent2}")

#         try:
#             agent2_response = requests.post("http://localhost:6010/ConfidenceScoreAgent/0.1", json=input_for_agent2)
#             agent2_response.raise_for_status()
#             agent2_output = extract_output_raw(agent2_response.text)
#             main_use_case1_logger.info(f"Response from Agent 2: {agent2_output}")
#         except requests.exceptions.RequestException as e:
#             main_use_case1_logger.error(f"Error calling Agent 2: {e}")
#             raise HTTPException(status_code=500, detail="Error processing request with Agent 2")
        
#         if isinstance(agent2_output, list):
#             average_confidence_score = agent2_output[0]["average_confidence_score"]
#         elif isinstance(agent2_output, dict):
#             average_confidence_score = agent2_output.get("average_confidence_score", 0)
#         elif isinstance(agent2_output,str):
#             agent2_output = json.loads(agent2_output)
#             average_confidence_score = agent2_output.get("average_confidence_score", 0)
#         else:
#             main_use_case1_logger.info("instance check else triggered!")
#             # average_confidence_score = 0
#         main_use_case1_logger.info(f"\nAVG SCORE:{average_confidence_score}")
#         if int(average_confidence_score) > 85 :
#             main_use_case1_logger.info("average_confidence_score is greater than threshold. Proceeding to Agent 3.")
#             input_for_agent3 = create_input_text(agent2_output)
#             main_use_case1_logger.info(f"Agent 3 input: {input_for_agent3}")

#             try:
#                 agent3_response = requests.post("http://localhost:7000/MappingAgent/0.1", json=input_for_agent3)
#                 agent3_response.raise_for_status()
#                 print(agent3_response.text)
#                 main_use_case1_logger.info(f"3 Output: {agent3_response.text}")
#                 main_use_case1_logger.info(f"Response from Agent 3: {extract_output_raw(str(agent3_response.text))}")
#             except requests.exceptions.RequestException as e:
#                 main_use_case1_logger.error(f"Error calling Agent 3: {e}")
#                 raise HTTPException(status_code=500, detail="Error processing request with Agent 3")

#             json_output = {
#                 "agent1_response": extract_output_raw(str(agent1_response.text)),
#                 "agent2_response": extract_output_raw(str(agent2_response.text)),
#                 "agent3_response": extract_output_raw(str(agent3_response.text))
#             }

#             main_use_case1_logger.info("Processing complete, returning response.")
            
#             main_use_case1_logger.info(f"Final Output:\n{json.dumps(json_output, indent=2)}")
#             return json_output
#         else:
#             json_output = {
#                 "agent1_response": extract_output_raw(str(agent1_response.text)),
#                 "agent2_response": extract_output_raw(str(agent2_response.text)),
#                 "agent3_response": None
#             }
#             main_use_case1_logger.info("average_confidence_score is less than threshold, terminating the process")
#             main_use_case1_logger.info(f"Final Output:\n{json.dumps(json_output, indent=2)}")
#             return json_output
#     else:
#         main_use_case1_logger.info("Email is not related to an order. Not sending to Agent 2.")
#         json_output = {
#                 "agent1_response": extract_output_raw(str(agent1_response.text)),
#                 "agent2_response": None, 
#                 "agent3_response": None
#             }
#         main_use_case1_logger.info(f"Final Output:\n{json.dumps(json_output, indent=2)}")
#         return json_output


# def extract_output_raw(json_string):
#     data = json.loads(json_string)
#     output_raw = data.get("output_raw")
#     if output_raw:
#         cleaned_output_raw = output_raw.strip("```json\n").strip("```").strip()
#         return json.loads(cleaned_output_raw)
#     else:
#         return None

# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=8000)
