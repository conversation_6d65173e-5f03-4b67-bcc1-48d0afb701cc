# ProductBot Agent

This agent is designed to be deployed alongside AgentGPTeal and help users with 2 tasks
1) Assist them if they are having trouble by leveraging information available to that product, by accessing a specific confluence page(i.e. a Product charter/product homepage) 
2) If they cannot assist them, then offering to create a Jira Ticket (or SNOW ticket down the line) to get support, request a feature or request access. The ticket will go on a specified board.


## Required Environment variables

```

TA_API_KEY=<Your API Key>
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06


TA_SERVICE_CONFIG=ProductBot/config.yaml
TA_PLUGIN_MODULE=ProductBot/custom_plugins.py
api_key_name = X-Merck-APIKey
TA_OTEL_ENDPOINT=http://aspire:18889


JIRA_API_BASE_URL=<the base url for jira api>
JIRA_API_KEY=<Your Jira Key>
JIRA_USERNAME=<username for a nonperson account to login to jira>
JIRA_PASSWORD=<password for a nonperson account to login to jira>
PROJECT_KEY = <Jira Board project key>

TA_CONFLUENCE_API_KEY=<Your Confluence Key>
TA_BASE_CONFLUENCE_URL=<the base url for confluence api>
SPACE_KEY = <Space key for conlfuence page>
PAGE_TITLE = <conflunence page title>
CONFLUENCE_USERNAME=<username for a nonperson account to login to confluence>
CONFLUENCE_PASSWORD=<password for a nonperson account to login to confluence>
```

### For Testing 
You can use the AgentGPTeal charter in the confluence test space, and the CDWS test jira board 
```
PROJECT_KEY = CDWS

SPACE_KEY = EG
PAGE_TITLE = agentGPTeal+Charter
```
