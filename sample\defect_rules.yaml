celonis_data_classification_rules: 
  - _CASE_KEY
  - _INVOICE_KEY
  - VENDOR_CODE
  - PAYMENT_METHOD
  - RISK_TEXT
  - AGENT_IDENTIFIED_RISK (give a short description of the type of risk identified)
  - AGENT_COMMNET

# defect_rules:
#   - rule_id: R1
#     description: Identify any record where the payment method has changed.
#   - rule_id: R2
#     description: Identify discrepancies in bank account details.
#   - rule_id: R3
#     description: Detect a record that contains potential risks or defects.
defect_rules:
  - rule_id: R1
    description: Identify any record where the payment method has changed, indicating a potential risk of payment processing discrepancies. This rule helps ensure that any changes in payment methods are flagged for review to prevent issues in transaction processing.
    comment_template: "Invoice {INVOICE_NUMBER} is impacted due to a change in payment method. The previous payment method was {PREVIOUS_PAYMENT_METHOD}, and the new payment method is {CURRENT_PAYMENT_METHOD}. This change may lead to discrepancies in payment processing."

  - rule_id: R2
    description: Identify discrepancies in bank account details that do not match the vendor master data. This rule is crucial for preventing payment errors and ensuring that funds are directed to the correct accounts, thereby minimizing financial risks.
    comment_template: "Invoice {INVOICE_NUMBER} has discrepancies in bank account details. The current bank account information does not match the vendor master data, which may lead to payment delays or errors."

  - rule_id: R3
    description: Detect records that contain potential risks or defects, which may impact the integrity of financial transactions. This rule aims to highlight any anomalies that could affect payment processes and require immediate attention to mitigate risks.
    comment_template: "Invoice {INVOICE_NUMBER} contains potential risks. The identified risk is: {RISK_TEXT}. This may affect the payment process and requires immediate attention."
