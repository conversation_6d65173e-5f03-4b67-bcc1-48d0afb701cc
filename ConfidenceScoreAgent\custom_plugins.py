import os
import sys
import json
import yaml
from typing import Dict, List, Annotated, Optional, Any
from semantic_kernel.functions import kernel_function
from semantic_kernel.functions.kernel_arguments import KernelArguments
from sk_agents.ska_types import BasePlugin
from logger.agent_loggers import Logger
confidence_logger = Logger()

class ConfidenceScorePlugin(BasePlugin):
    def __init__(self, authorization: Optional[Dict[str, Any]] = None, extra_data_collector: Optional[Any] = None):
        """
        Initialize the ConfidenceScorePlugin.
        """
        confidence_logger.info("Initializing ConfidenceScorePlugin")
        
        # Store framework parameters
        self.authorization = authorization
        self.extra_data_collector = extra_data_collector
        
        # Find the root directory and sample directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        root_dir = os.path.dirname(current_dir)
        sample_dir = os.path.join(root_dir, "sample")
        
        # Set file paths
        self.mapping_file = os.path.join(sample_dir, "mapping.yaml")
        confidence_logger.info(f"Mapping file path: {self.mapping_file}")


    @kernel_function(
        name="get_mapping_rules",
        description="Get the mapping rules from the mapping file."
    )
    def get_mapping_rules(self) -> str:
        """
        Read and return the mapping rules as a JSON string.
        """
        confidence_logger.info("Executing get_mapping_rules function")
        
        try:
            with open(self.mapping_file, "r", encoding="utf-8") as file:
                mapping_rules = yaml.safe_load(file)
                confidence_logger.info("Successfully read mapping rules file")
                return json.dumps(mapping_rules, indent=2)
        except Exception as e:
            confidence_logger.error(f"Error reading mapping rules file: {e}")
            return json.dumps({"error": f"Error reading mapping rules file: {str(e)}"}, indent=2)

    @kernel_function(
    name="process_notification_status",
    description="Process the results and add notification_sent field based on manual_review_needed status"
    )
    def process_notification_status(self, result_json: str) -> str:
        """
        Process the results from task 2 and add notification_sent field.
        """
        confidence_logger.info("Processing notification status based on manual review flag")
        confidence_logger.debug(f"Input type: {type(result_json)}")
        confidence_logger.debug(f"Input preview: {result_json[:100]}...")
        
        try:
            # First, try to extract JSON if it's wrapped in markdown code blocks
            if isinstance(result_json, str) and "```" in result_json:
                import re
                json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', result_json)
                if json_match:
                    result_json = json_match.group(1).strip()
                    confidence_logger.info("Extracted JSON from markdown code block")
            
            # Parse the JSON string
            try:
                parsed_data = json.loads(result_json)
                confidence_logger.debug(f"Successfully parsed JSON. Type: {type(parsed_data)}")
            except json.JSONDecodeError as e:
                confidence_logger.error(f"JSON parsing error: {e}")
                return json.dumps({"error": f"Invalid JSON: {str(e)}", "input": result_json[:200]})
            
            # Create a new result list to store processed emails
            processed_result = []
            
            # Handle different data structures
            if isinstance(parsed_data, dict):
                # Check if it has an 'emails' key
                if "emails" in parsed_data:
                    confidence_logger.debug("Found 'emails' key in parsed data")
                    emails = parsed_data["emails"]
                else:
                    # Single email object
                    confidence_logger.debug("Treating parsed data as a single email object")
                    emails = [parsed_data]
            elif isinstance(parsed_data, list):
                confidence_logger.debug(f"Parsed data is a list with {len(parsed_data)} items")
                emails = parsed_data
            else:
                confidence_logger.error(f"Unexpected data type: {type(parsed_data)}")

                return json.dumps({"error": f"Unexpected data type: {type(parsed_data)}"})
            
            # Process each email
            for i, item in enumerate(emails):
                confidence_logger.debug(f"Processing item {i}, type: {type(item)}")
                
                # Create a new email object
                email_obj = {}
                
                # If the item is a string (the source of our error), handle it specially
                if isinstance(item, str):
                    confidence_logger.debug(f"Item {i} is a string: {item[:50]}...")
                    try:
                        # Try to parse it as JSON
                        email_obj = json.loads(item)
                    except:
                        # If parsing fails, create a simple object
                        email_obj = {
                            "text": item,
                            "manual_review_needed": True,
                            "notification_sent": False
                        }
                elif isinstance(item, dict):
                    # If it's already a dictionary, use it
                    email_obj = item
                else:
                    # For any other type, create a simple object
                    confidence_logger.debug(f"Item {i} has unexpected type: {type(item)}")
                    email_obj = {
                        "value": str(item),
                        "manual_review_needed": True,
                        "notification_sent": False
                    }
                
                # Now safely set the notification_sent field
                if email_obj.get("manual_review_needed", True):
                    email_obj["notification_sent"] = False
                else:
                    email_obj["notification_sent"] = True
                
                processed_result.append(email_obj)
            
            # Return the processed result
            confidence_logger.info(f"Successfully processed {len(processed_result)} emails")
            confidence_logger.info(f"Final O/P - {processed_result}")
            return json.dumps(processed_result, indent=2)
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            confidence_logger.error(f"Error processing notification status: {e}")
            confidence_logger.debug(f"Traceback: {error_details}")
            return json.dumps({
                "error": f"Error processing notification status: {str(e)}",
                "traceback": error_details,
                "input_preview": str(result_json)[:200] if isinstance(result_json, str) else str(result_json)
            }, indent=2)






# # Second approach(working code) with sepearte kernel function including logger
# import os
# import sys
# import json
# import yaml
# from typing import Dict, List, Annotated, Optional, Any
# from semantic_kernel.functions import kernel_function
# from semantic_kernel.functions.kernel_arguments import KernelArguments
# from semantic_kernel.connectors.ai.open_ai import OpenAIPromptExecutionSettings
# from sk_agents.ska_types import BasePlugin
# from logger.agent_loggers import Logger
# confidence_logger = Logger()

# # Helper Function: Load YAML classification rules
# def load_mapping_rules(file_path: str) -> Dict:
#     try:
#         with open(file_path, "r") as file:
#             data = yaml.safe_load(file)
#         return data
#     except FileNotFoundError:
#         confidence_logger.error(f"Mapping file not found: {file_path}")
#         return {"mappings": []}
#     except Exception as e:
#         confidence_logger.error(f"Error loading mapping rules: {e}")
#         return {"mappings": []}

# class ConfidenceScorePlugin(BasePlugin):
#     def __init__(self, authorization: Optional[Dict[str, Any]] = None, extra_data_collector: Optional[Any] = None):
#         """
#         Initialize the ConfidenceScorePlugin.
#         """
#         confidence_logger.info("Initializing ConfidenceScorePlugin")
        
#         # Store framework parameters
#         self.authorization = authorization
#         self.extra_data_collector = extra_data_collector
        
#         # Find the root directory and sample directory
#         current_dir = os.path.dirname(os.path.abspath(__file__))
#         root_dir = os.path.dirname(current_dir)
#         sample_dir = os.path.join(root_dir, "sample")
        
#         # Set file paths
#         self.email_file = os.path.join(sample_dir, "emaildata.json")
#         self.mapping_file = os.path.join(sample_dir, "mapping.yaml")
        
#         confidence_logger.info(f"Email file path: {self.email_file}")
#         confidence_logger.info(f"Mapping file path: {self.mapping_file}")

#     @kernel_function(
#         name="data_extractor",
#         description="Initialize the plugin and return the email data."
#     )
#     def extract_multiple_emails_content(self) -> str:
#         """
#         Read and return the email data as a JSON string.
#         """
#         confidence_logger.info("Executing data_extractor function to provide email data")
        
#         try:
#             with open(self.email_file, "r", encoding="utf-8") as file:
#                 email_data = file.read()
#                 confidence_logger.info("Successfully read email data file")
#                 return email_data
#         except Exception as e:
#             confidence_logger.error(f"Error reading email data file: {e}")
#             return json.dumps({"error": f"Error reading email data file: {str(e)}"}, indent=2)

#     @kernel_function(
#         description="Get the mapping rules from the mapping file."
#     )
#     def get_mapping_rules(self) -> str:
#         """
#         Return the mapping rules as a JSON string.
#         """
#         confidence_logger.info("Inside get_mapping_rules function")
        
#         try:
#             # Load mapping rules from file
#             with open(self.mapping_file, "r", encoding="utf-8") as file:
#                 mapping_rules = yaml.safe_load(file)
#                 confidence_logger.info("Successfully loaded mapping rules")
                
#             return json.dumps(mapping_rules, indent=2)
#         except Exception as e:
#             confidence_logger.error(f"Error getting mapping rules: {e}")
#             return json.dumps({"error": f"Error getting mapping rules: {str(e)}"}, indent=2)

#     @kernel_function(
#         description="Map email fields according to mapping rules and calculate confidence scores."
#     )
#     async def map_email_fields(self, kernel) -> str:
#         """
#         Process email data using LLM to map fields according to mapping rules.
        
#         Returns:
#             JSON string containing mapped fields with confidence scores
#         """
#         try:
#             # Get email data
#             confidence_logger.info("Getting email data for mapping")
#             email_data_json = self.extract_multiple_emails_content()
#             email_data = json.loads(email_data_json)
            
#             # Get mapping rules
#             confidence_logger.info("Getting mapping rules")
#             mapping_rules_json = self.get_mapping_rules()
#             mapping_rules = json.loads(mapping_rules_json)
            
#             # Create prompt for the LLM
#             confidence_logger.debug("Creating prompt for field mapping")
#             prompt = f"""
#             You are an expert in extracting structured information from emails.
            
#             TASK:
#             Map fields from the provided emails according to the mapping rules.
            
#             MAPPING RULES:
#             {json.dumps(mapping_rules, indent=2)}
            
#             EMAIL DATA:
#             {json.dumps(email_data, indent=2)}
            
#             INSTRUCTIONS:
#             1. For each email, extract fields according to the mapping rules
#             2. For each extracted field, assign a confidence score between 0-100 based on how certain you are about the extraction
#             3. Return a JSON array with the following structure for each email:
            
#             {{
#               "email_id": "unique-id-from-input",
#               "mapped_fields": [
#                 {{
#                   "field_name": "field name from mapping rules",
#                   "value": "extracted value",
#                   "confidence_score": 95.5
#                 }}
#               ]
#             }}
            
#             Only return valid JSON without any explanations or additional text.
#             """
            
#             # Call the LLM
#             confidence_logger.info("Calling LLM for field mapping")
#             settings = OpenAIPromptExecutionSettings(
#                 service_id="default",
#                 ai_model_id="gpt-4o-2024-08-06",
#                 max_tokens=4000,
#                 temperature=0.0
#             )
            
#             # THIS IS THE LLM CALL
#             confidence_logger.debug("Invoking LLM prompt for field mapping")
#             result = await kernel.invoke_prompt(
#                 prompt=prompt,
#                 arguments=KernelArguments(),
#                 settings=settings
#             )
            
#             confidence_logger.info("Successfully mapped email fields")
#             return result.value
#         except Exception as e:
#             confidence_logger.error(f"Error mapping email fields: {e}")
#             return json.dumps({
#                 "error": f"Error mapping email fields: {str(e)}"
#             }, indent=2)

#     @kernel_function(
#         description="Calculate overall confidence scores and determine if manual review is needed."
#     )
#     async def calculate_confidence_scores(self, kernel, mapped_fields_json: str) -> str:
#         """
#         Calculate overall confidence scores and determine if manual review is needed.
        
#         Args:
#             mapped_fields_json: JSON string containing mapped fields with confidence scores
            
#         Returns:
#             JSON string containing processed email data with overall confidence scores
#         """
#         try:
#             # Parse the mapped fields
#             confidence_logger.info("Calculating confidence scores")
#             confidence_logger.debug(f"Input data type: {type(mapped_fields_json)}")
            
#             mapped_data = json.loads(mapped_fields_json)
#             confidence_logger.debug(f"Successfully parsed mapped fields JSON")
            
#             # Create prompt for the LLM
#             confidence_logger.debug("Creating prompt for confidence score calculation")
#             prompt = f"""
#             You are an expert in evaluating data quality and confidence scores.
            
#             TASK:
#             Calculate overall confidence scores for each email and determine if manual review is needed.
            
#             MAPPED EMAIL DATA:
#             {json.dumps(mapped_data, indent=2)}
            
#             INSTRUCTIONS:
#             1. For each email, calculate the average confidence score across all mapped fields
#             2. Determine if manual review is needed (score < 94%)
#             3. Return a JSON with the following structure:
            
#             {{
#             "emails": [
#                 {{
#                 "email_id": "unique-id-from-input",
#                 "average_confidence_score": 98.5,
#                 "manual_review_needed": false,
#                 "mapped_fields": [same as input]
#                 }}
#             ]
#             }}
            
#             Only return valid JSON without any explanations or additional text.
#             """
            
#             # Call the LLM
#             confidence_logger.info("Calling LLM for confidence score calculation")
#             settings = OpenAIPromptExecutionSettings(
#                 service_id="default",
#                 ai_model_id="gpt-4o-2024-08-06",
#                 max_tokens=4000,
#                 temperature=0.0
#             )
            
#             # THIS IS THE SECOND LLM CALL
#             confidence_logger.debug("Invoking LLM prompt for confidence score calculation")
#             result = await kernel.invoke_prompt(
#                 prompt=prompt,
#                 arguments=KernelArguments(),
#                 settings=settings
#             )
            
#             confidence_logger.info("Successfully calculated confidence scores")
#             return result.value
#         except Exception as e:
#             confidence_logger.error(f"Error calculating confidence scores: {e}")
#             return json.dumps({
#                 "status": "error",
#                 "message": f"Error calculating confidence scores: {str(e)}",
#                 "processed_emails": 0,
#                 "emails": []
#             }, indent=2)