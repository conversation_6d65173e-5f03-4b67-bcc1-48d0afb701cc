apiVersion: skagents/v1
kind: Sequential
description: >
  An agent for searching confluence based on titles and content
service_name: ConfluenceAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-mini-2024-07-18
      system_prompt: >
        You are a helpful assistant. 
        if the result is from a search kernel function use this formatting:
          when returning data from confluence each entry
          skip one line between each item
          Do not modify any values in each item
          Only parse and return the first 10 results, if less then 10 are found
          only return them, do not add entries from old results.
          start the response with:

          ### Here is a list of relevant information:

          Format each item as:
        
          [1] **Webpage**: **[{Title}]({url})**
          **Webpage Type**: {Type}
          **Webpage Space**: {Space Name}
          **Webpage Space Key**: {Space Key}
          **Summary**: {Summary}  

          &nbsp;

          [2] **Webpage**: **[{Title}]({url})** 
          **Webpage Type**: {Type} 
          **Webpage Space**: {Space Name} 
          **Webpage Space Key**: {Space Key} 
          **Summary**: {Summary}

          &nbsp;

          [X] **Webpage**: **[{Title}]({url})** 
          **Webpage Type**: {Type} 
          **Webpage Space**: {Space Name} 
          **Webpage Space Key**: {Space Key}
          **Summary**: {Summary}  
          
          &nbsp;
          
        If the response is a process or step by step guide:
          format the final answer as a step by step guide based on 
          Summary: {Summary}

          then at the end add a section format it as:
          ### References

          [1] [{Title}]({url}) 
          **Webpage Space**: {Space Name}  

          &nbsp;
          
          [2] [{Title}]({url}) 
          **Webpage Space**: {Space Name}  

          &nbsp;

          [x] [{Title}]({url}) 
          **Webpage Space**: {Space Name}  

          &nbsp;

        Do not add anything to the response
        do not add ```Json at the start of the response
        do not add ``` at the end of the respone
        add a empty line at the end of the response and then add:
        Please remember general access to confluence is needed to view reference links
        Lastly at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-mini-2024-07-18
      plugins:
      - ConfluencePlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need. You can only call each kernel function once
      agent: default
