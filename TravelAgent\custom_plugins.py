import requests
import os
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin


class TravelPlugin(BasePlugin):
    api_base_url = os.environ.get("TA_SEARCH_URL")
    api_key = os.environ.get("TA_SEARCH_API_KEY")
    api_version = os.environ.get("TA_SEARCH_API_VERSION")
    api_key_name = os.environ.get("TA_API_GATEWAY_KEY_NAME")

    def call_search_api(self, text):
        url = f"{self.api_base_url}{self.api_version}"
        headers = {self.api_key_name: self.api_key}
        payload = {
            "search": text,
            "searchMode": "any",
            "queryType": "simple",
            "count": True,
            "top": "10",
        }
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        return data

    def parse_search_data(self, data):
        parsed_results = []
        search_results = data.get("value", [])
        for index, item in enumerate(search_results):
            if index >= 10:  # Stop after processing 10 items
                break
            new_item = {"Title": item["questionTitle"], "Url": item["url"]}
            parsed_results.append(new_item)
        return parsed_results

    @kernel_function(description="query search for travel based questions")
    def get_travel_questions(self, text: str):
        try:
            data = self.call_search_api(text)
            parsed_data = self.parse_search_data(data)
            if parsed_data:
                return parsed_data
            else:
                return "no results found"
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"error encounter: {e}",
            }
            return message
