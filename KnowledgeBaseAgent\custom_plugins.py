import requests
import os
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from openai import AzureOpenAI
from sk_agents.ska_types import BasePlugin


class KnowledgeBasePlugin(BasePlugin):
    api_base_url = os.environ.get("TA_SNOW_KB_URL")
    api_key = os.environ.get("TA_SNOW_KB_API_KEY")
    api_version = os.environ.get("TA_SNOW_KB_API_VERSION")
    api_key_name = os.environ.get("TA_API_GATEWAY_KEY_NAME")

    def call_openai_embedding(self, prompt: str) -> list[float]:
        client = AzureOpenAI(
            api_key=os.environ.get("TA_API_KEY"),
            api_version="2024-09-01-preview",
            azure_endpoint=os.environ.get("TA_BASE_URL"),
        )
        embeddings = client.embeddings.create(
            input=prompt,
            model=os.environ.get("TA_SNOW_KB_EMBEDDING_MODEL"),
        )
        return embeddings.data[0].embedding

    def call_search_api(self, text: str, embedding: list[float]):
        url = f"{self.api_base_url}{self.api_version}"
        headers = {self.api_key_name: self.api_key}
        payload = {
            "count": True,
            "search": text,
            "top": 5,
            "select": "id, ESC_PARENT_SNTITLE, ESC_PARENT_SNDESC, ESC_PARENT_ESCBASE_URL, ESC_body, ESC_embedding",
            "vectorQueries": [
                {
                    "kind": "vector",
                    "vector": embedding,
                    "exhaustive": True,
                    "fields": "ESC_embedding",
                    "k": 5,
                }
            ],
        }
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        return data

    def parse_search_data(self, data):
        parsed_results = []
        search_results = data.get("value", [])
        for index, item in enumerate(search_results):
            if index >= 5:  # Stop after processing 5 items
                break
            new_item = {
                "Title": item["ESC_PARENT_SNTITLE"],
                "Body": item["ESC_body"],
                "Url": item["ESC_PARENT_ESCBASE_URL"],
                "Description": item["ESC_PARENT_SNDESC"],
            }
            parsed_results.append(new_item)
        return parsed_results

    @kernel_function(
        description="query search for ServiceNow Knowledge Base information"
    )
    def get_info(self, text: str):
        try:
            embedding = self.call_openai_embedding(text)
            data = self.call_search_api(text, embedding)
            parsed_data = self.parse_search_data(data)
            if parsed_data:
                return parsed_data
            else:
                return "no results found"
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"error encounter: {e}",
            }
            return message
