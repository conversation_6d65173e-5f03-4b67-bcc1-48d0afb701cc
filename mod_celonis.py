# pip install pip_system_certs 
# pip install --extra-index-url=https://pypi.celonis.cloud/ pycelonis
# pip install --extra-index-url=https://pypi.celonis.cloud/ pycelonis --upgrade
# print(pycelonis.__version__)
# pip show pycelonis

import pip_system_certs.wrapt_requests
import pycelonis
import pandas as pd
from io import StringIO
from datetime import date, datetime

API_KEY='ODdmNDNjYzAtYjE0MC00YTI2LTkyZWUtMTk5NTZmM2IwYmQ4OjB5SnNTTlBUYUE0ZHB2d0E4ZVZtcSsxS0hMSnNHREIyUlk3dnBKOTY0dzR3'
TEAM_URL='https://merck-tst.us-1.celonis.cloud/'
DATA_POOL='MDL_PQL_STSGlobalDMInvoicesatRisk'
DATA_MODEL='MDL_PQL_STSGlobalDMInvoicesatRisk'
DATA_ROWS=1

def run_join_query():
    celonis = pycelonis.get_celonis(base_url=TEAM_URL, api_token=API_KEY, permissions=False)
    dp = celonis.data_integration.get_data_pools().find(DATA_POOL)
    dm = dp.get_data_models().find(DATA_MODEL)
    
    from pycelonis.pql import PQL, PQLColumn, PQLFilter
    
    # Create a PQL query with the join
    query = PQL(distinct=False, limit=DATA_ROWS)
    
    # Add specific columns first
    query += PQLColumn(query='"INVOICES_AT_RISK"."REASONS"', name="REASONS")
    query += PQLColumn(query='"INVOICES_AT_RISK"."PAYMENT_METHOD"', name="PAYMENT_METHOD")
    query += PQLColumn(query='"RISK_DETAILS_VIEW"."RISK_TEXT"', name="RISK_TEXT")
    
    # Add all other columns from INVOICES_AT_RISK
    ir_table = dm.get_tables().find("INVOICES_AT_RISK")
    for col in ir_table.get_columns():
        col_name = col.name
        if col_name not in ["REASONS", "PAYMENT_METHOD"]:  # Skip already added columns
            query += PQLColumn(query=f'"INVOICES_AT_RISK"."{col_name}"', name=f"IR_{col_name}")
    
    # Add JOIN condition
    query += PQLFilter(query='FILTER "INVOICES_AT_RISK"."REASONS" = "RISK_DETAILS_VIEW"."CODE"')
    
    # Add WHERE condition
    query += PQLFilter(query='FILTER "INVOICES_AT_RISK"."REASONS" IN (\'0208\')')
    
    # Execute the query
    df = dm.export_data_frame(query)
    return df if len(df) > 0 else None

# Run the query
data_frame = run_join_query()

if data_frame is not None:
    # Save to text file in key-value pair format
    with open('text1.txt', 'w') as file:
        for index, row in data_frame.iterrows():
            for col in data_frame.columns:
                file.write(f"{col}: {row[col]}\n")
            file.write("\n")  # Add a newline for separation between rows
    print("Data written to text1.txt in key-value pair format.")
    
    # Also save as CSV for easier analysis
    data_frame.to_csv('query_results.csv', index=False)
    print("Data also saved to query_results.csv")
else:
    print("No data returned.")