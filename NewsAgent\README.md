# News Agent

This agent is design to interact with a rag based search tool to retrieve the 
top ten current news items and then using the document body generate a summary 
of all the different ongoing news items for the response and provide the list
of urls used. Please be aware this agent is designed with the assumption a tool
like Kong, API Portal, etc

## Environment Variables for local running

```text
TA_API_KEY=< Your GPTeal API Key>
TA_SERVICE_CONFIG=NewsAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_SEARCH_URL=<Your Search URL>
TA_SEARCH_API_KEY=<Your API Key>
TA_SEARCH_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Environment Variables for platform deployment

```text
TA_API_KEY=< Your GPTeal API Key>
TA_SERVICE_CONFIG=NewsAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_SEARCH_URL=<Your Search URL>
TA_SEARCH_API_KEY=<Your API Key>
TA_SEARCH_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Dedicated Hardware

This agent currently does not require any addition hardware or software to run
