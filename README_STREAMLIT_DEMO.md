# Agent Orchestration Streamlit Demo

This is a standalone Streamlit application that provides a user-friendly interface for demonstrating the agent orchestration workflow. The application allows users to upload email files and attachments, then visualize the processing through the three-agent pipeline.

## Features

### 🎯 Core Functionality
- **File Upload Interface**: Support for email files (.eml, .msg, .txt) and attachments (.csv, .pdf, .xlsx)
- **Real-time Workflow Visualization**: Live status updates for each agent in the pipeline
- **Side-by-side Comparison**: Original content vs. processed results
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Service Health Monitoring**: Automatic detection of running services

### 🤖 Agent Pipeline Visualization
1. **EmailClassifyingAgent** (Port 5000) - Classifies email content
2. **ConfidenceScoreAgent** (Port 6010) - Calculates confidence scores (if classified as order)
3. **MappingAgent** (Port 7000) - Maps data to target format (if confidence > 85%)

### 📊 Data Processing
- Automatic extraction of key-value pairs from uploaded files
- Support for various email formats
- CSV attachment processing with confidence scores
- JSON formatting for easy readability

## Prerequisites

### Required Services
Before running the Streamlit demo, ensure the following services are running:

1. **Individual Agent Services**:
   ```bash
   # Terminal 1: EmailClassifyingAgent
   python agent1_uc1.py  # Runs on port 5000
   
   # Terminal 2: ConfidenceScoreAgent  
   python agent2_uc1.py  # Runs on port 6010
   
   # Terminal 3: MappingAgent
   python agent3_uc1.py  # Runs on port 7000
   ```

2. **Orchestration Service**:
   ```bash
   # Terminal 4: Main orchestration service
   python main_run_uc1.py  # Runs on port 8000
   ```

### Python Environment
- Python 3.11 or higher
- Virtual environment recommended

## Installation & Setup

### 1. Install Dependencies
```bash
# Install Streamlit demo dependencies
pip install -r requirements_streamlit.txt
```

### 2. Verify Service Dependencies
The demo application uses the existing agent infrastructure. Ensure you have the main project dependencies installed:
```bash
# Install main project dependencies (if not already done)
uv sync --native-tls
```

### 3. Start Required Services
Follow the prerequisites section to start all required services.

## Running the Demo

### Start the Streamlit Application
```bash
streamlit run streamlit_demo_app.py
```

The application will be available at: `http://localhost:8501`

### Using the Demo

1. **Check Service Status**: The sidebar shows the status of the orchestration service
2. **Upload Files**: 
   - Upload an email file (required)
   - Optionally upload attachment files
3. **Process Files**: Click "Start Agent Processing" to begin the workflow
4. **Monitor Progress**: Watch real-time status updates for each agent
5. **View Results**: Examine the final JSON output and individual agent responses

## File Format Support

### Email Files
- **`.eml`**: Standard email format (RFC 822)
- **`.msg`**: Outlook message format
- **`.txt`**: Plain text email content

### Attachment Files
- **`.csv`**: Comma-separated values (processed as key-value pairs)
- **`.pdf`**: PDF documents (sample data generated for demo)
- **`.xlsx`**: Excel spreadsheets (processed as tabular data)

## Demo Data Structure

The application converts uploaded files into the expected format for the agent pipeline:

```json
{
  "email_id": "demo_upload",
  "email_data": [
    {
      "key": "From:",
      "Key_confidence_score": 95.0,
      "value": "<EMAIL>",
      "Value_confidence_score": 95.0
    }
  ],
  "attachment_data": [
    {
      "key": "Document Type:",
      "Key_confidence_score": 95.0,
      "value": "Purchase Order",
      "Value_confidence_score": 95.0
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **"Orchestration service is not available"**
   - Ensure `main_run_uc1.py` is running on port 8000
   - Check if the service is accessible at `http://localhost:8000`

2. **Agent Processing Fails**
   - Verify all individual agents are running on their respective ports
   - Check agent logs for specific error messages

3. **File Upload Errors**
   - Ensure file formats are supported
   - Check file size limitations
   - Verify file is not corrupted

4. **Timeout Errors**
   - Increase timeout values in the API client if needed
   - Check network connectivity to localhost services

### Service Health Check
The application automatically checks service health. If services are not available:
- Red indicator: Service is down
- Green indicator: Service is running

### Debug Mode
For additional debugging information, you can run Streamlit in debug mode:
```bash
streamlit run streamlit_demo_app.py --logger.level=debug
```

## Architecture

### Standalone Design
- **Independent Module**: Does not modify existing agent code
- **API Integration**: Uses existing FastAPI endpoints
- **Separate Dependencies**: Has its own requirements file
- **Self-contained**: Can be run independently of the main application

### Data Flow
1. User uploads files through Streamlit interface
2. Files are processed and converted to expected format
3. Data is sent to orchestration API (`/master/agent`)
4. Real-time status updates are displayed
5. Final results are formatted and presented

## Limitations

### Demo Scope
- **Demonstration Purpose**: Optimized for visual clarity, not production performance
- **File Processing**: Simplified extraction logic for demo purposes
- **Security**: No authentication or file validation for production use
- **Scalability**: Single-user interface, not designed for concurrent users

### File Processing
- **Email Parsing**: Basic extraction, may not handle complex email formats
- **Attachment Processing**: Limited to CSV for full processing, others use sample data
- **Size Limits**: Subject to Streamlit's default file upload limits

## Future Enhancements

### Potential Improvements
- **Enhanced Email Parsing**: More sophisticated email content extraction
- **Real-time Streaming**: WebSocket integration for live updates
- **File Validation**: Enhanced security and validation
- **Multi-user Support**: Session management for concurrent users
- **Export Functionality**: Download processed results
- **Configuration UI**: Dynamic service endpoint configuration

## Support

For issues specific to the Streamlit demo:
1. Check the troubleshooting section above
2. Verify all prerequisite services are running
3. Review the console output for error messages
4. Ensure file formats are supported

For issues with the underlying agent services, refer to the main project documentation.
