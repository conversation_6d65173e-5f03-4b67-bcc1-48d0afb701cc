apiVersion: skagents/v1
kind: Sequential
description: >
  Confidence Score and Field Extraction Agent
service_name: ConfidenceScoreAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: confidence_score_agent
      role: Confidence Score Agent
      model: "gpt-4o-2024-08-06"
      system_prompt: >
        You are an assistant responsible for extracting email data and calculating confidence scores.
        
        Your tasks include processing emails as below:
        1. Mapping fields according to predefined rules
        2. Calculating confidence scores for each field and email
        3. Determining if manual review is needed based on confidence scores
        
        When processing emails:
        - Extract relevant information from the provided email data
        - Apply mapping rules to identify specific fields
        - Calculate confidence scores based on the extracted text quality
        - Flag emails for manual review if their average confidence score is below 94%
        
        Always return results in a structured JSON format without any additional text or explanations.
      plugins:
        - ConfidenceScorePlugin
  tasks:
    - name: map_email_fields_task
      task_no: 1
      description: Map email fields according to mapping rules and calculate field-level confidence scores
      instructions: >
        Follow these steps to map email fields:
        
        1. You will receive emaildata 
        2. Call the 'get_mapping_rules' function to get the mapping rules
        3. For the received email data:
           a. Parse the email data (it may be a JSON string or already structured data)
           b. Apply mapping rules to identify specific fields
           c. Calculate confidence scores for each extracted field
        
        4. Return a structured JSON response with:
           - For each email:
             * Email ID
             * List of mapped fields with their values and confidence scores
      agent: confidence_score_agent
      
    - name: calculate_confidence_scores_task
      task_no: 2
      description: Calculate overall confidence scores and determine if manual review is needed
      instructions: >
        Follow these steps to calculate overall confidence scores:
        
        1. Take the mapped fields from the previous task
        2. For each email:
           a. Calculate the average confidence score across all mapped fields
           b. Determine if manual review is needed (score < 85%)
        
        3. Return a structured JSON response with:
             * average_confidence_score(Always place this field as the first element in the response and not enclosed inside the datastructure)
             * Email ID
             * Whether manual review is needed
             * List of mapped fields with their values and confidence scores
             * If multiple products are present, then provide separate JSON mappings for each product and its details
        4. Only provide the JSON output. Do not add any additional text or explanations.
      agent: confidence_score_agent
      
    - name: process_notification_status_task
      task_no: 3
      description: Process the results and add notification status based on manual review flag
      instructions: >
        Take the results from the previous task and add notification status:
        
        1. Call the 'process_notification_status' function with the results from the previous task
        2. Return the processed JSON output with notification_sent field added
        
        The output should be a structured JSON with all previous fields plus:
        - notification_sent: false if manual_review_needed is true, false otherwise
      agent: confidence_score_agent
  file_paths:
    EMAIL_FILE: "../sample/emaildata.json"
    MAPPING_FILE: "../sample/mapping.yaml"







# # Second approach(working code) with three tasks
# apiVersion: skagents/v1
# kind: Sequential
# description: >
#   Confidence Score and Field Extraction Agent
# service_name: ConfidenceScoreAgent
# version: 0.1
# input_type: BaseInput
# spec:
#   agents:
#     - name: confidence_score_agent
#       role: Confidence Score Agent
#       model: "gpt-4o-2024-08-06"
#       system_prompt: >
#         You are an assistant responsible for extracting email data and calculating confidence scores.
        
#         Your tasks include:
#         1. Processing emails from JSON data
#         2. Mapping fields according to predefined rules
#         3. Calculating confidence scores for each field and email
#         4. Determining if manual review is needed based on confidence scores
        
#         When processing emails:
#         - Extract relevant information from subject, body, and attachments
#         - Apply mapping rules to identify specific fields
#         - Calculate confidence scores based on the extracted text quality
#         - Flag emails for manual review if their average confidence score is below 94%
        
#         Always return results in a structured JSON format.
#       plugins:
#         - ConfidenceScorePlugin
#   tasks:
#     - name: map_email_fields_task
#       task_no: 1
#       description: Map email fields according to mapping rules and calculate field-level confidence scores
#       instructions: >
#         Follow these steps to map email fields:
        
#         1. Call the 'extract_multiple_emails_content' function to get email data
#         2. Call the 'get_mapping_rules' function to get mapping rules
#         3. Call the 'map_email_fields' function to process the emails and extract fields
        
#         The 'map_email_fields' function will:
#         - Extract fields from each email according to mapping rules
#         - Calculate confidence scores for each extracted field
#         - Return a structured JSON with mapped fields and their confidence scores
#       agent: confidence_score_agent
      
#     - name: calculate_confidence_scores_task
#       task_no: 2
#       description: Calculate overall confidence scores and determine if manual review is needed
#       instructions: >
#         Follow these steps to calculate overall confidence scores:
        
#         1. Take the mapped fields from the previous task
#         2. Call the 'calculate_confidence_scores' function with the mapped fields
        
#         The 'calculate_confidence_scores' function will:
#         - Calculate the average confidence score for each email
#         - Determine if manual review is needed (score < 94%)
#         - Return a structured JSON with:
#           * For each email:
#             - Email ID
#             - Average confidence score
#             - Whether manual review is needed
#             - List of mapped fields with their values and confidence scores
#       agent: confidence_score_agent
      
#   file_paths:
#     EMAIL_FILE: "../sample/emaildata.json"
#     MAPPING_FILE: "../sample/mapping.yaml"