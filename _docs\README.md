# Local Agent Setup Instructions

This document provides a comprehensive guide to setting up agent configurations on macOS, covering installations, setup, testing, troubleshooting, and environment variables.

## Table of Contents

1.  [Required Installations](#1-required-installations)
2.  [Agent Setup Instructions](#2-agent-setup-instructions)
4.  [Troubleshooting](#3-troubleshooting)

## 1. Required Installations

Before you begin, ensure you have the following tools installed on your macOS system:

-   **Xcode:** Provides essential macOS development tools.
-   **Homebrew:** A package manager for macOS, simplifying software installation.
-   **Git/GitHub:** For managing code repositories and version control.
-   **uv:** A fast and modern Python package manager.
-   **Make:** A command-line utility for task automation.

### Installation Commands

Open your terminal and execute the following commands to install the necessary software:

```bash
# Install Xcode command line tools
xcode-select --install

# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
# Install required packages
brew install git
brew install gh
brew install make
brew install uv
# Add shell completion for uv
echo 'eval "$(uv generate-shell-completion zsh)"' >> ~/.zshrc
echo 'eval "$(uvx --generate-shell-completion zsh)"' >> ~/.zshrc

# Authenticate GitHub CLI
gh auth login

# Clone the agent configurations repository
git clone https://github.com/merck-gen/hai-gpteal-agent-configs.git
```

## 2. Agent Setup Instructions
After setting up your local environment (macOS, Linux, or Windows), you can test a basic agent without plugins. Follow these steps:

### Step 1: Navigate to the Cloned Repository
```bash
cd hai-gpteal-agent-configs
```

### Step 2: Set Up the Environment and Test the Agent
Run the following commands to create a basic environment and test the setup:
```bash
make environment-mac
```

### Step 3: Enter required inputs
The make run will require two inputs, a `TA_API_KEY` and `TA_SERVICE_CONFIG`
```bash
Please enter your TA_API_KEY
API Key: <Your API Key>
Updated API key.

Please enter your TA_SERVICE_CONFIG e.g. ChatAgent/config.yaml
Agent service configuration path: <Your Agent Folder>/config.yaml
Updated agent service configuration.
```
For an example, you can enter `TA_SERVICE_CONFIG` as "_docs/tutorials/01_getting_started/config.yaml"

### Step 4 (Optional): Additional Dependencies
<details>
<summary><i>Click here to install additional dependencies</i></summary>
Prior to testing your agent, you must install the required dependencies. To do so, from the root of this repository, run a uv sync or uv sync --native-tls if it is your first time running uv sync locally.

```bash
uv sync
```
or
```bash
uv sync --native-tls 
```

If you have additional dependencies specified in a requirements.txt file in your agent folder, install them using:
```bash
uv pip install -r <AgentFolder>/requirements.txt
```
</details>


### Step 5: Run the Agent
You can run the agent by running the following `make` command:
```bash
make test
```

<details>
<summary><i>Alternative ways to run the agent</i></summary> 
Optionally you can also run the agent by running the following `uv` commands:

```bash
# Using uv
uv run -- python agent.py

# Activating the virtual environment and running the agent
source .venv/bin/activate
python agent.py

# Using make
make test
```
</details>


### Step 6: Access the Agent's REST Endpoint
Test your agent's REST endpoint by visiting:
```bash
http://localhost:8110/<AgentName>/<AgentVersion>/docs
```
For example:
```bash
http://localhost:8110/ChatBot/0.1/docs
```
To stop the agent, use Ctrl+C in the terminal.

**Note**: For more complex agents, refer to the agent README for additional environment variables needed for the `.env` file. For instructions on creating agent configurations and code, see the [Agent Building Tutorials](tutorials/01_getting_started).


## 3. Troubleshooting

### Certificate Errors
If you encounter **SSL** or **CERTIFICATE_VERIFY_FAILED** errors while using an external service/API (e.g., ChatWeatherAgent), you may need to add the Merck root certificate to your Python environment. Follow these steps:

#### Step 1: Start a Python Shell with uv
```bash
bash
uv run python
```
#### Step 2: Find the Path to Your Certificate Store
Run the following commands:
```bash
python
import certifi
print(certifi.where())
```
Copy the output path (e.g., `/path/to/your/venv/cert/store/cacert.pem`).

#### Step 3: Exit the Python Shell
```bash
python
quit()
```
#### Step 4: Append the Merck Root Certificate
Append the Merck root certificate to the copied path:
```bash
cat _build/Merck-ICS-PROD-ROOT-CA-G2.crt >> /path/to/your/venv/cert/store/cacert.pem
```

### Environment Variables Errors

You can also verify that your environment variables are configured correctly to avoid errors such as `No such file or directory` or `AuthenticationError`. Double-check the `.env` file you created earlier (when running `make environment-mac`). It should contain at least the following content. You can find an example in the root directory as `.env.example`:

```bash
TA_API_KEY=<Your API Key>
TA_SERVICE_CONFIG=<Your Agent Folder>/config.yaml
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_OTEL_ENDPOINT=http://aspire:18889
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLED=false
```
Make sure to include any other properties needed for your agent, such as `TA_TYPES_MODULE` or `TA_PLUGIN_MODULE`.

## Conclusion

Congratulations! You've successfully set up and tested your agent. If you have any further questions or need assistance, feel free to ask!