import json
import requests
import os
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin


class JiraPlugin(BasePlugin):
    api_base_url = os.environ.get("JIRA_API_BASE_URL")
    username = os.environ.get("JIRA_USERNAME")
    password = os.environ.get("JIRA_PASSWORD")
    api_key = os.environ.get("JIRA_API_KEY")
    api_key_name = os.environ.get("api_key_name")
    project_key = os.environ.get("PROJECT_KEY")

    def call_jira_api_for_ticket(self, ticket_id):
        url = f"{self.api_base_url}/issue/{ticket_id}?fields=summary,status,description,created"
        headers = {self.api_key_name: self.api_key}
        response = requests.get(
            url, headers=headers, auth=HTTPBasic<PERSON>uth(self.username, self.password)
        )
        if response.status_code != 200:
            raise Exception(
                f"Failed to retrieve ticket: {response.status_code} - {response.text}"
            )
        return response.json()

    def parse_jira_data(self, data):
        return {
            "key": data["key"],
            "summary": data["fields"]["summary"],
            "description": data["fields"]["description"],
            "status": data["fields"]["status"]["name"],
            "created": data["fields"]["created"],
        }

    @kernel_function(
        description="Retrieve summary, description, status, and creation time for a given Jira ticket"
    )
    def get_jira_ticket_details(self, ticket_id: str):
        try:
            data = self.call_jira_api(ticket_id)
            parsed_data = self.parse_jira_data(data)
            return parsed_data
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"Error encountered: {e}",
            }
            return message

    @kernel_function(description="Create a Jira ticket")
    def create_jira_ticket(self, project_key, summary, reporter, description):
        url = f"{self.api_base_url}/issue"

        issue_data = {
            "fields": {
                "project": {"key": self.project_key},  # Replace with your project ID
                "summary": summary,
                "issuetype": {
                    "name": "Service Request"  # 1 - Bug, 3 - task, 10600-Service Request,
                },
                "reporter": {"name": reporter},  # Replace with the reporter's username
                "description": description,
            }
        }

        headers = {"Content-Type": "application/json", self.api_key_name: self.api_key}

        response = requests.post(
            url,
            headers=headers,
            data=json.dumps(issue_data),
            auth=HTTPBasicAuth(self.username, self.password),
        )

        response.raise_for_status()
        data = response.json()
        return data


class ConfluencePlugin(BasePlugin):
    api_base_url = os.environ.get("TA_BASE_CONFLUENCE_URL")
    username = os.environ.get("CONFLUENCE_USERNAME")
    password = os.environ.get("CONFLUENCE_PASSWORD")
    api_key = os.environ.get("TA_CONFLUENCE_API_KEY")
    api_key_name = os.environ.get("api_key_name")
    space_key = os.environ.get("SPACE_KEY")
    page_title = os.environ.get("PAGE_TITLE")

    def call_confluence_api(self):
        url = f"{self.api_base_url}/content?spaceKey={self.space_key}&title={self.page_title}&expand=body.view"
        headers = {self.api_key_name: self.api_key}
        response = requests.get(
            url, headers=headers, auth=HTTPBasicAuth(self.username, self.password)
        )
        response.raise_for_status()
        data = response.json()
        return data

    def parse_confluence_data(self, data):
        results = data.get("results", [])
        for item in results:
            # Accessing keys
            item_id = item.get("id")
            item_type = item.get("type")
            item_status = item.get("status")
            item_title = item.get("title")
            item_position = item.get("position")
            item_value = item.get("body", {}).get("view", {}).get("value", "")
        return {"Page Title": item_title, "Page Content": item_value}

    @kernel_function(
        description="Retrieve page contents for a given space key and page name"
    )
    def get_confluence_page(self):
        try:
            data = self.call_confluence_api()
            parsed_data = self.parse_confluence_data(data)
            return parsed_data
        except Exception as e:
            message = {
                "result": "failed",
                "response": f"Error encountered: {e}",
            }
            return message
