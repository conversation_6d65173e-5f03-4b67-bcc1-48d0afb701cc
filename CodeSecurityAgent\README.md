# Security Agent

This agent is a code review agent the main purpose of it is to handle reviewing
code submitted to it and then reviewing the code for any potential security issues
that could exist in the code. The agent will identify the issue, suggest remediations
and then provide test cases for the code

## Environment variables for local development

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=SecurityAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
```

## Environment variables for production development

```text
TA_API_KEY=<Your GPTeal API Key>
TA_SERVICE_CONFIG=SecurityAgent/config.yaml
```