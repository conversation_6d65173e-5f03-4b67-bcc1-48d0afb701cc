import asyncio
import unittest
import json

# Adjust the import based on your project structure.
from custom_plugins import RecommendDefectChangesAgent

class TestRecommendDefectChangesPlugin(unittest.TestCase):
    """
    Unit tests for the RecommendDefectChangesAgent class.

    This test suite verifies the functionality of the RecommendDefectChangesAgent,
    which is responsible for recommending changes based on provided input. 
    The tests cover the following key functionalities:

    1. **Change Recommendation**:
       - `test_recommend_defects`: Ensures that the `recommend_changes` method
         correctly processes the input data and recommends changes related to
         discrepancies in payment details. This includes verifying that the expected
         fields such as `_CASE_KEY`, `_INVOICE_KEY`, `RISK_TEXT`, 
         `AGENT_COMMNET`, and `AGENT_IDENTIFIED_RISK` are present in the result.

    Each test method uses asynchronous execution to simulate the necessary
    components and isolates the functionality of the RecommendDefectChangesAgent,
    ensuring that the tests are reliable and do not depend on external files or states.
    """

    def test_recommend_defects(self):
        """Test the asynchronous recommend_changes method simulating API output."""
        # Provided input data as a JSON string
        input_json_string = "{\"_CASE_KEY\": \"1109330510946374720240010208\", \"_INVOICE_KEY\": \"110933051094637472024001\", \"VENDOR_CODE\": \"0003097722\", \"PAYMENT_METHOD\": \"M\", \"RISK_TEXT\": \"Payment Method on the Invoice and Vendor Master Data does not match.\", \"AGENT_IDENTIFIED_RISK\": \"The payment method has changed, indicating a potential risk of payment processing discrepancies.\", \"AGENT_COMMNET\": \"Invoice 35765 is impacted due to a change in payment method. The previous payment method was not provided, and the new payment method is M. This change may lead to discrepancies in payment processing.\"}"

        # Convert the JSON string to a Python dictionary
        sample_input = json.loads(input_json_string)

        plugin = RecommendDefectChangesAgent(None)
        result = asyncio.run(plugin.recommend_changes(sample_input))        
        # Check that the expected fields are present in the result
        self.assertIn("_CASE_KEY", result)
        self.assertIn("_INVOICE_KEY", result)
        self.assertIn("RISK_TEXT", result)
        self.assertIn("VENDOR_CODE", result)
        self.assertIn("PAYMENT_METHOD", result)
        self.assertIn("AGENT_IDENTIFIED_RISK", result)
        self.assertIn("AGENT_COMMNET", result)
        self.assertIn("AGENT_RECOMMENDED_CHANGE", result)
        

if __name__ == '__main__':
    unittest.main(verbosity=2)