# 1. DepartmentAssignmentAgent

## Overview
The **DepartmentAssignmentAgent** processes defect data and assigns departments based on the defect type using predefined rules. It analyzes the data from the RecommendDefectChangesAgent and applies the rules specified in the `department_assignment_rules.yaml` file.

## 2. Features
- Assigns departments to defects based on their risk type.
- Utilizes a set of department assignment rules defined in a YAML file.
- Returns results as a structured JSON array containing defects with assigned departments.

## 3. Dependencies
 
This agent depends on the following files:
- **Configuration File:** `DepartmentAssignment/config.yaml`
-
- **Rules File:** `../sample/recommendation_rules.yaml`
 
### Environment Variables

Create a `.env` file at `DepartmentAssignmentAgent/.env` with the following required values:
 
```plaintext
TA_API_KEY="Use your KEY here"
TA_SERVICE_CONFIG=DepartmentAssignmentAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=misc/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
```
 
## 4. How the Agent Works
 
The agent operates through the following workflow:
 
1. **Configuration Loading:**  
   Loads configuration from `DepartmentAssignmentAgent/config.yaml`, which defines its structure, input types, and processing agents.
 
2. **Plugin Initialization:**  
   Imports `custom_plugins.py`, where the `DepartmentAssignmentPlugin` class is defined. This class initializes necessary parameters and loads classification rules from the rules file.
 
3. **Assign Department**  
   When recommendation data is received, the `assign_department` function is called within the `DepartmentAssignmentPlugin`. This function:
   - Analyzes the input data based on the classification rules.
   - Constructs a prompt for the Azure OpenAI model to identify defects.
   - Calls the OpenAI API with the constructed prompt.
 
4. **Result Generation:**  
   Returns the identification result in a structured JSON format, including the assigned department and their descriptions.
 
5. **Logging:**  
   A log file is created in the `/sample/` directory for each run, named with the format `agent_log_YYYY-MM-DD_HH-MM-SS` (e.g., `agent_log_2025-05-29_12-43-57`).
 

## 5. Usage
 
To run the agent, follow these steps:
 
1. Navigate to the main folder.
2. Run the agent:
   ```bash
   python agent3_uc2.py
   ```

 
## 6. Sample Output
 
Here is a sample output from the last successful run of the IdentifyDefectAgent:
 
```json
[
  {
    "process_id": "P-123456",
    "risk_type": "Payment Details Change",
    "defect_summary": "Payment details were changed without proper approval or audit trail.",
    "recommended_change": "Implement stricter approval processes and enforce audit logging.",
    "risk_severity": "High",
    "risk_priority": "High",
    "assigned_department":"Risk Assessment Team"
  }
]
```

## 7. Unit Testing
 
A unit test case file is located at `DepartmentAssignmentAgent/tests/test_custom_plugins.py`. To run the test cases, use:
 
```bash
python -m unittest -v .\tests\test_custom_plugins.py
```
 
## 8. API Access
 
Once running, access the Swagger documentation at:  
[http://localhost:8110/DepartmentAssignmentAgent/0.1/docs](http://localhost:8110/DepartmentAssignmentAgent/0.1/docs)