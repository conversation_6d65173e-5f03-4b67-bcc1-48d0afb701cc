apiVersion: skagents/v1
kind: Sequential
description: >
  A SAP Order Mapping Agent
service_name: MappingAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-08-06
      system_prompt: >
        You are a helpful assistant for mapping order data to SAP order fields.
        Your task is to process order data and map it to the SAP fields using the provided rules.
        For each email, extract relevant information and create a structured output following the SAP format.
        Ensure all mapped fields, including nested fields, are correctly identified and mapped to the appropriate SAP fields.
      plugins:
        - OrderMappingPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Map  orders data to SAP fields.
      instructions: >
        1. Call the 'map_order_fields' function.
        2. The function will receive the data as input.
        3. Ensure that all fields from the order_mapping.json.
        4. Return the mapped results as a JSON array.
        5. Don't add any extra text or explanation to json output.
        6. Directly provide the Output of 'map_order_fields' function as it is.Do not add any extra details or explanation.
      agent: default
  file_paths:
    SAP_RULES_FILE: "../sample/sap_rules.yaml"