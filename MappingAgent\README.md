# Mapping Agent README

## 1. Overview
The SAP Order Mapping Agent takes mapped data from ConfidenceScoreAgent and maps it to SAP fields using predefined rules. It ensures that all relevant fields are correctly identified and mapped to the appropriate SAP format.

## 2. Features
Takes mapped data from ConfidenceScore Agent.
Maps the data to SAP fields using predefined rules.
Returns results in a structured JSON format.
Logs mapping activities for monitoring and debugging.

## 3. Dependencies
This agent depends on the following files:
Main Agent File: gbsdsai-agent-configs/agent3_uc1.py
AWS connector file : aws_connector.py
Configuration File: MappingAgent/config.yaml
Rules File: ../sample/sap_rules.yaml
Material Details File: ../sample/Productdetails.xls

### Environment Variables
A .env file needs to be created at MappingAgent/.env with the following required values:

AZURE_OPENAI_API_KEY="your API key"
AZURE_OPENAI_ENDPOINT=https://iapi-test.merck.com/gpt/libsupport
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-11-20
API_VERSION=2024-09-01-preview
SERVICE_ID=azure_openai
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_SERVICE_CONFIG=MappingAgent/config.yaml


## 4. How the Agent Works

Configuration Loading:
The agent loads its configuration from MappingAgent/config.yaml, defining its structure, input types, and tasks.

Plugin Initialization:
The OrderMappingPlugin class is initialized from custom_plugins.py, setting up logging and loading mapping rules from sap_rules.yaml.

Email Processing:
Task : Map Order Fields:
Takes mapped data from ConfidenceScoreAgent as input.
Loads mapping rules from sap_rules.yaml.
Applies mapping rules to extract fields and map them to SAP format.
Calls the OpenAI API to perform semantic analysis and mapping.
Reads product details from S3 using readProductDetailsFromS3.
Matches material ID from the mapped JSON with the product details.
Updates the product name in the mapped JSON if necessary.
Returns a JSON with mapped SAP fields.

Logging:
Logs actions and events using the Logger class.
Creates a log file in the /sample/ directory for each run, named with the format DDMMYY+time.

## 5. Usage

To run the agent, follow these steps:
Navigate to the main folder.
Activate the virtual environment using the following command:

.venv/scripts/activate
Run the agent with the command:

python gbsdsai-agent-configs/agent3_uc1.py

## 6 Sample Output
{
    "email_id": "20250519_164933",
    "product_name": "PREVYMIS 240MG 4X7TAB COL",
    "order_date": "2025-05-09",
    "item_count": "14",
    "material_id": "1041849"
  }


## 7 . Testing

The agent includes a suite of unit tests located at MappingAgent/tests/test_custom_plugins.py. These tests verify the functionality of the OrderMappingPlugin class.

Primary Tests Included:
Field Mapping:
Test: test_map_order_fields
Purpose: Ensures that the map_order_fields method correctly maps order fields from the provided sample order mapping JSON data according to the specified SAP rules. This includes verifying that the expected fields such as order_id, product_name, order_date, and item_count are present in the result.

Running the Tests:
Navigate to the MappingAgent folder.
Execute the following command in the terminal:

python -m unittest -v .\tests\test_custom_plugins.py


## 8. API Access
Once running, access the Swagger documentation at:
http://localhost:7000/MappingAgent/0.1/docs