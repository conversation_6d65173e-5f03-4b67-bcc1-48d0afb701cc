import asyncio
import unittest

# Adjust the import based on your project structure.
from custom_plugins import IdentifyDefectPlugin

class TestIdentifyDefectPlugin(unittest.TestCase):
    """
    Unit tests for the IdentifyDefectPlugin class.

    This test suite verifies the functionality of the IdentifyDefectPlugin,
    which is responsible for identifying defects based on provided input data. 
    The tests cover the following key functionalities:

    1. **Defect Identification**:
       - `test_identify_defects`: Ensures that the `identify_defects` method
         correctly processes the input data and identifies defects related to
         changes in payment details. This includes verifying that the expected
         fields such as `process_id`, `vendor_id`, `risk_description`, 
         `comment`, and `risk_type` are present in the result.

    Each test method uses asynchronous execution to simulate the necessary
    components and isolates the functionality of the IdentifyDefectPlugin,
    ensuring that the tests are reliable and do not depend on external files or states.
    """

    def test_identify_defects(self):
        """Test the asynchronous identify_defects method simulating API output."""
        # Sample Celonis input data (as Python dict)
        sample_input = {
            "data": [
                {
                    "process_id": "P-123456",
                    "vendor_id": "V-987654",
                    "vendor_name": "GlobalTech Ltd.",
                    "payment_details_change_date": "2025-04-20",
                    "previous_payment_details": {
                        "bank_account": "**********************",
                        "payment_method": "Bank Transfer"
                    },
                    "new_payment_details": {
                        "bank_account": "**********************",
                        "payment_method": "Bank Transfer"
                    },
                    "risk_identified": True,
                    "risk_type": "Payment Details Change",
                    "risk_description": "Payment details were changed without proper approval or audit trail.",
                    "risk_severity": "High",
                    "process_step": "Vendor Master Data Update",
                    "detected_by": "Automated Risk Detection"
                }
            ]
        }

        plugin = IdentifyDefectPlugin(None)
        result = asyncio.run(plugin.identify_defects(sample_input))
        self.assertIn("RISK_TEXT", result)
        self.assertIn("AGENT_IDENTIFIED_RISK", result)
        self.assertIn("AGENT_COMMNET", result)
        self.assertIn("_CASE_KEY", result)
        self.assertIn("_INVOICE_KEY", result)
        self.assertIn("VENDOR_CODE", result)
        self.assertIn("PAYMENT_METHOD", result)



if __name__ == '__main__':
    unittest.main(verbosity=2)