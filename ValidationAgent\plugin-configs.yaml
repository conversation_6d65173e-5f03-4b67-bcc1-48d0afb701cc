openai:
  api_base: "https://iapi-test.merck.com/gpt/libsupport"
  api_version: "2023-05-15"
  embedding_model: "text-embedding-ada-002-2"
  api_key: ""

qdrant:
  host: "localhost:6333"
  collection_name: "doc_completion_test_collection"
  vector_size: 1536
  column_name: "scope_embedding"

QASR-QualityAssuranceSummaryReport:
  Scope:
    output_format: >
      '''
                      Scope:
                This document applies specifically to [insert system name] deployed for [insert usage context, e.g., global use, regional use, etc.].
 
                Scope of Changes:
                This deliverable applies specifically to [insert project or system name], version [insert version number] implementation. [Insert project or system name] (the application) is used as an official repository to create, approve, and store [insert relevant documents or items, e.g., QAP, RS, etc.].
 
                The primary goal of the release is to implement the following:
                - [Insert primary goal of the release]
                - [Insert additional goals or features]
                - [Insert additional goals or features]


                [Insert any additional relevant information about change management or automation]

                Intended Audience :
                - Insert any information regarding intended audience.

                Enhancements:
                - [Insert enhancement details]
                - [Insert enhancement details]

                

                Bug Fixes:
                - [Insert bug fix details]
                - [Insert bug fix details]
                - [Insert bug fix details]
      '''
    checklist: >
      '''
      QASR Scope Review Checklist

          General Requirements – High
              1. Clarity - High
                  -Is the scope articulated in clear and understandable language? (Yes/No)
                  -Is technical jargon minimized or clearly defined? (Yes/No)
                  -Is the scope written in past or present perfect tense?(Yes/No)
                  -Is the scope written coherently, no nonsense or filler text?(Yes/No)
                  -Does the text contain links?(Highly recommended that links are not used anywhere in the documentation)
              2. Completeness - High
                  -Does the scope provide all necessary information regarding the system and release? (Yes/No)
                  -Is the scope written in complete sentences?(Yes/No)
          Context - High
              1. System Definition - High
                  -Is the system clearly defined (name, version, etc.)? (Yes/No)
                  -Is the release version explicitly stated (e.g., Digital SDLC version 4.14.0)? (Yes/No)
              2. Geographical Scope - High
                  -Does the scope specify the intended site, region, or country of use? (Yes/No)
                  -Is the deployment context (production, staging, etc.) clearly defined? (Yes/No)
              3. Intended Audience - Medium
                  -Is the intended audience for the QASR clearly identified (e.g., SDLC practitioners, management)? (Yes/No)
          Purpose and Justification – High
              1. Reasoning for Changes - High
                  -Is there a clear rationale provided for the changes being made? (Yes/No)
                  -Does the document explain why the system needs these changes or updates? (Yes/No)
          Primary Goals – High
              1. Goals Enumeration - High
                  -Does the scope outline the primary goals of the release as a bulleted list? (Yes/No)
                  -Are the specific changes categorized appropriately (e.g., new features, bug fixes, enhancements)? (Yes/No)
              2. Detailed Descriptions - Medium
                  -Are each of the primary goals briefly described to convey what they entail? (Yes/No)
                  -Are enhancements, bug fixes, and new features differentiated in sub-lists? (Yes/No)
          Change Management - Low
              1.Change Management Overview - Low
                  -Is there a mention of how changes will be managed or implemented? (Yes/No)
                  -Are any automation or process improvements included (e.g., Change Management Automation)? (Yes/No)
          Limitations and Exclusions - Low
              1. Scope Limitations - Low
                  -Are any limitations of the scope identified (e.g., what is not covered by the QASR)? (Yes/No)
                  -Are dependencies and assumptions related to the scope documented? (Yes/No)
          '''
