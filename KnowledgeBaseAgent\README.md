# Knowledge Base Agent

This agent is designed to interact with a rag based search tool to retrieve the most relevant 
ServiceNow Knowledge Base items and then using the document body to generate a summary 
of all the different relevant IT information for the response and provide the list
of urls used. Please be aware this agent is designed with the assumption a tool
like Kong, API Portal, etc

## Environment Variables for local deployment

```text
TA_API_KEY=<Your API Key>
TA_SERVICE_CONFIG=KnowledgeBaseAgent/config.yaml
TA_OTEL_ENDPOINT=http://aspire:18889
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=_build/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06
TA_SNOW_KB_EMBEDDING_MODEL=text-embedding-3-large
TA_SNOW_KB_URL=<Your Search URL>
TA_SNOW_KB_KEY=<Your API Key >
TA_SNOW_KB_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Environment Variables for platform deployment

```text
TA_API_KEY=<Your API Key>
TA_SERVICE_CONFIG=KnowledgeBaseAgent/config.yaml
TA_OTEL_ENDPOINT=http://aspire:18889
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_SNOW_KB_EMBEDDING_MODEL=text-embedding-3-large
TA_SNOW_KB_URL=<Your Search URL>
TA_SNOW_KB_KEY=<Your API Key >
TA_SNOW_KB_API_VERSION=<Your API Version>
TA_API_GATEWAY_KEY_NAME=<Your API Key field>
```

## Dedicated Hardware

This agent currently does not require any additional hardware or software to run
