[project]
name = "hai-gpteal-agent-configs"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pandas>=2.2.3",
    "ruff>=0.11.2",
    "sk-agents>=0.1.16",
    "ska-utils>=0.1.15",
]

[tool.uv.sources]
ska-utils = { index = "devkubeconcept" }
sk-agents = { index = "devkubeconcept" }

[[tool.uv.index]]
name = "devkubeconcept"
url = "https://artifacts.merck.com/artifactory/docker-kubeconcept-dev/python-wheel"
