from typing import Any, Dict, Optional
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
import requests
import json
import uvicorn
import os
from logger.agent_loggers import Logger

logger = Logger()

app = FastAPI(title="Agent Orchestration API")

class InputData(BaseModel):
    input_text: dict

class ResponseModel(BaseModel):
    agent1_response: Any
    agent2_response: Any
    agent3_response: Any

def create_input_text(input_data: dict) -> dict:
    input_string = json.dumps(input_data)
    enclosed_json_string = f'"{input_string}"'
    return {
        "chat_history": [
            {
                "role": "user",
                "content": enclosed_json_string
            }
        ]
    }

def load_default_input() -> dict:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(current_dir, "sample", "use_case2_celonis_input.json")
    try:
        with open(input_file, 'r') as file:
            return json.load(file)
    except Exception as e:
        logger.error(f"Error loading default input: {e}")
        return {}

def get_input_data(data: InputData = None) -> dict:
    return data.input_text if data and data.input_text else load_default_input()

def extract_output_raw(json_string):
    try:
        data = json.loads(json_string)
        output_raw = data.get("output_raw")
        if output_raw:
            cleaned_output_raw = output_raw.strip("```json\n").strip("```").strip()
            return json.loads(cleaned_output_raw)
        return None
    except Exception as e:
        logger.error(f"Error extracting output_raw: {e}")
        return None

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(status_code=422, content={"detail": exc.errors()})

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled error: {exc}")
    return JSONResponse(status_code=500, content={"detail": "An unexpected error occurred."})

def call_agent(agent_number: int, url: str, input_data: dict) -> Any:
    try:
        response = requests.post(url, json=input_data)
        response.raise_for_status()
        return extract_output_raw(response.text)
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error calling Agent {agent_number}: {e}")
        raise HTTPException(status_code=e.response.status_code, detail=f"HTTP error with Agent {agent_number}: {e.response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling Agent {agent_number}: {e}")
        raise HTTPException(status_code=500, detail={"detail": f"Error processing request with Agent {agent_number}", "error": str(e)})

@app.post("/master/agent", response_model=ResponseModel)
def process(data: InputData = None):
    input_data = get_input_data(data)
    
    agent1_data = call_agent(1, "http://localhost:5000/IdentifyDefectAgent/0.1", create_input_text(input_data))
    logger.info(f"Response from Agent 1: {agent1_data}")

    agent2_data = call_agent(2, "http://localhost:6010/RecommendDefectChangesAgent/0.1", create_input_text(agent1_data))
    logger.info(f"Response from Agent 2: {agent2_data}")

    agent3_data = call_agent(3, "http://localhost:7000/DeptAssignment/0.1", create_input_text(agent2_data))
    logger.info(f"Response from Agent 3: {agent3_data}")

    return ResponseModel(agent1_response=agent1_data, agent2_response=agent2_data, agent3_response=agent3_data)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)







# from typing import Any, Dict, List, Optional, Union
# from fastapi import FastAPI, Body, HTTPException, Depends
# from pydantic import BaseModel
# import requests
# import json
# import uvicorn
# import os

# app = FastAPI(title="Agent Orchestration API")

# class InputData(BaseModel):
#     input_text: dict

# class ResponseModel(BaseModel):
#     agent1_response: Any
#     agent2_response: Any
#     agent3_response: Any

# def create_input_text(input_data: dict) -> dict:
#     """Helper function to create input text for agents."""
#     input_string = json.dumps(input_data)
#     enclosed_json_string = f'"{input_string}"'
#     return {
#         "chat_history": [
#             {
#                 "role": "user",
#                 "content": enclosed_json_string
#             }
#         ]
#     }

# def load_default_input() -> dict:
#     """Load default input data from the JSON file."""
#     current_dir = os.path.dirname(os.path.abspath(__file__))
#     input_file = os.path.join(current_dir, "sample", "use_case2_celonis_input.json")
    
#     try:
#         with open(input_file, 'r') as file:
#             return json.load(file)
#     except Exception as e:
#         print(f"Error loading default input: {e}")
#         return {}

# def get_input_data(data: InputData = None) -> dict:
#     """Get input data from request or use default if none provided."""
#     if data and hasattr(data, 'input_text') and data.input_text:
#         return data.input_text
#     return load_default_input()

# def extract_output_raw(json_string):
#     try:
#         data = json.loads(json_string)
#         output_raw = data.get("output_raw")
#         if output_raw:
#             cleaned_output_raw = output_raw.strip("```json\n").strip("```").strip()
#             return json.loads(cleaned_output_raw)
#         else:
#             return None
#     except Exception as e:
#         print(f"Error extracting output_raw: {e}")
#         return None

# @app.post("/master/agent", response_model=ResponseModel)
# def process(data: InputData = None):
#     # Get input data from request or use default
#     input_data = get_input_data(data)
    
#     # Process with Agent 1
#     input_for_agent1 = create_input_text(input_data)
#     print(f"Agent 1 received: {input_for_agent1}")
#     agent1_response = requests.post("http://localhost:5000/IdentifyDefectAgent/0.1", json=input_for_agent1)
#     print("\n\n RESPONSE OF AGENT 1:", agent1_response.text)
#     agent1_data = extract_output_raw(agent1_response.text)

#     # Process with Agent 2
#     input_for_agent2 = create_input_text(agent1_data)
#     agent2_response = requests.post("http://localhost:6010/RecommendDefectChangesAgent/0.1", json=input_for_agent2)
#     print("\n\nRESPONSE OF AGENT2:", agent2_response.text)
#     agent2_data = extract_output_raw(agent2_response.text)

#     # Process with Agent 3
#     input_for_agent3 = create_input_text(agent2_data)
#     agent3_response = requests.post("http://localhost:7000/DeptAssignment/0.1", json=input_for_agent3)
#     print("\n\nRESPONSE OF AGENT3:", agent3_response.text)
#     agent3_data = extract_output_raw(agent3_response.text)

#     # Return the response in the expected format
#     return ResponseModel(
#         agent1_response=agent1_data,
#         agent2_response=agent2_data,
#         agent3_response=agent3_data
#     )

# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=8000)