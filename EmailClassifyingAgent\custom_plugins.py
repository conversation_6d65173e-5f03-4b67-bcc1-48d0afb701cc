import os
import sys
import json
import yaml
import traceback
from typing import Dict, List, Annotated, Optional, Any
from semantic_kernel.functions import kernel_function
from semantic_kernel.functions.kernel_arguments import KernelArguments
from semantic_kernel.connectors.ai.open_ai import OpenAIPromptExecutionSettings
from sk_agents.ska_types import BasePlugin
from openai import AzureOpenAI
from dotenv import load_dotenv

from logger.agent_loggers import Logger
classification_logger = Logger()

load_dotenv()

# Helper Function: Load YAML classification rules
def load_classification_rules(file_path: str) -> List[str]:
    try:
        with open(file_path, "r") as file:
            data = yaml.safe_load(file)
        rules = data.get("order_classification_rules", [])
        classification_logger.debug(f"Raw rules data: {rules}")

        if isinstance(rules, str):
            rules = [rule.strip() for rule in rules.split(',') if rule.strip()]
        elif isinstance(rules, list):
            rules = [str(rule).strip() for rule in rules if str(rule).strip()]
        else:
            rules = []

        classification_logger.info(f"Successfully loaded {len(rules)} classification rules")
        classification_logger.debug(f"Loaded rules: {rules}")    

        return rules
    
    except FileNotFoundError:
        classification_logger.error(f" Warning Rules file not found at {file_path}. No rules loaded.")
        return []
    
    except Exception as e:
        classification_logger.error(f"Error loading rules from {file_path}: {e}. No rules loaded.")
        return []

# Load configuration from a YAML file
def load_config(config_path: str) -> Dict:

    classification_logger.info(f"Loading configuration from {config_path}")
    try:
        with open(config_path, "r") as file:
            return yaml.safe_load(file)
        
        classification_logger.info(f"Successfully loaded configuration from {config_path}")

    except FileNotFoundError:
        classification_logger.error(f"Configuration file not found at {config_path}")
        
        return {}
    
    except Exception as e:

        classification_logger.error(f"Error loading configuration from {config_path}: {str(e)}")
        
        return {}
    


# A plugin for classifying emails based on predefined rules.
 
class EmailClassifyingPlugin(BasePlugin):

   
    def __init__(self, authorization: Optional[Dict[str, Any]] = None, extra_data_collector: Optional[Any] = None):

        """
        Initialize the EmailClassifyingPlugin.

        This constructor sets up the EmailClassifyingPlugin by initializing its parameters,
        loading classification rules from a specified YAML file, and determining the necessary
        file paths for configuration and rules.

        """
        # Store framework parameters
        self.authorization = authorization
        self.extra_data_collector = extra_data_collector
        
        # Find the root directory and sample directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        root_dir = os.path.dirname(current_dir)
        sample_dir = os.path.join(root_dir, "sample")
        
        # Set file paths
        self.rules_file = os.path.join(sample_dir, "order_rules.yaml")
        self.config_file = os.path.join(os.path.dirname(current_dir), "EmailClassifyingAgent", "config.yaml")
        
        # Load rules
        self.rules = load_classification_rules(self.rules_file)
       
        
        # Initialize Azure OpenAI client
        self.azure_openai = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )
         

               

    @kernel_function(
        name="classify_email",
        description="Analyze the extracted content and classify the email as either order or not order."
    )
    async def classify_email(self, data) -> str:
        """
        Analyze the extracted content and classify the email as either order or not order.
        
        Returns:
            JSON string containing the classification result
        """
        
        try:
            classification_logger.info(f"Starting email classification for email ID: {data['email_id']}")

            # Get the classification rules
            rules_string = ", ".join(self.rules)

            classification_logger.debug(f"Using classification rules: {rules_string}")

                 
            # Create prompt for the LLM
            prompt = (
                f"You are an email classification agent specializing in Spanish language emails.\n"
                f"Analyze the email content (which is primarily in Spanish) and classify it strictly as either 'order' or 'not order'.\n"
                f"Email Content:\n---\n{data}\n---\n\n"
                f"Consider email content and  these Spanish keywords/phrases often associated with orders: {rules_string}\n\n"
                f"Based ONLY on the content and the keywords, is this an order-related email? Respond ONLY with the word 'order' or the word 'not order'."
            )

            classification_logger.debug("Constructed LLM prompt for classification")

            # Call the LLM using kernel
            

            settings = OpenAIPromptExecutionSettings(
                service_id=os.getenv("SERVICE_ID"),
                ai_model_id=os.getenv("TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL"),
                max_tokens=4000,
                temperature=0.0
            )

            # Call the OpenAI API with the constructed prompt to classify the email
            classification_logger.info("Calling OpenAI API for email classification")

            response_text = self.call_openai(prompt)

            classification_logger.info(f"Email classification completed successfully. Result: {response_text}")
            
            # Create final result
            results = self.create_response_json(data,response_text)
            
            # Return the results as JSON
            return json.dumps(json.dumps(results, indent=2))
            
        except Exception as e:
            
            error_message = f"Error classifying email: {str(e)}"
            classification_logger.error(error_message)
            classification_logger.error(traceback.format_exc())
            return json.dumps({"error": error_message})
        
   
    # This function creates a JSON response object using the provided email data and response text
    def create_response_json(self,email_data,response_text):

        """
    Creates a standardized JSON response object based on the classification result.
    
    Parameters:
    -----------
    response_text : str
        The classification result from the OpenAI model, expected to be either 
        "order" or "not order"
    
    Returns:
    --------
    dict
        A dictionary with the following structure:
        {

            "classification": str,      # Either "order" or "not order"
            "email_data": dict          # "emailId" with  The complete extracted email data 
        }
        
        """

        # Validate response
        if response_text != "order" and response_text != "not order":
            
            classification_logger.error(f"LLM returned an unexpected response: '{response_text}'. Defaulting to 'not order'.")
            response_text = "not order"
        
        # Create the result dictionary
        result = {
            "classification": response_text,
            "email_data":email_data
        }
    
        return result
    


    # This method calls the Azure OpenAI API with the given prompt and returns the response text

    @staticmethod
    def call_openai(prompt: str) -> str:
        client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )
           
       
        completion = client.beta.chat.completions.parse(
            model=os.getenv("TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL"),
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )
        return completion.choices[0].message.content.strip().lower()
    
   

