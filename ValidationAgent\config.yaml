apiVersion: skagents/v1
kind: Sequential
description: >
  Agent to generate Validation report and Improvement Suggestions
service_name: SectionContentValidator
version: 0.1
input_type: ValidationInput
output_type: ValidationOutput
spec:
  agents:
    - name: QASpecialistAgent
      role: Quality Assurance Agent
      model: gpt-4o-2024-08-06
      temperature: 0.2
      system_prompt: >
        You are a Compliance Reviewer and Quality Assurance Specialist , tasked with validating the given section content of the given document.Your role involves reviewing this section against the provided System Overview and validation checklist and generate a detailed validation report with improvement suggestions.
        Based on the improvement suggestions you have to rewrite the section content that can be recommended to the user. Ensure that it is accurate and modified as per the validation report and improvement suggestions. For guidance, a few examples of the section content are provided to help you understand the desired writing style and overall structure.
      plugins:
        - Checklist

  tasks:
    - name: Validate_section_content
      task_no: 1
      description: Check compliance
      instructions: >
        Input: Receive the following inputs: 
          - Application name: Name of the application for which the Document is written
          - Document Type: Type of Document under review  
          - Section Name : Section title that needs to be reviewed and rewritten
          - Section content: The specific text of the section from the given document.
          - System Overview: A brief description of the system that includes its main functions and purpose.
          - Validation Checklist: A detailed checklist covering aspects such as clarity, relevance, completeness, and compliance. Plugin to fetch validation checklist
          - Historical Samples: Plugin to fetch relevant samples as examples
          - Do not mention High Level Requirements if not provided as input or is NA.
        Tasks -

        Step 1. Validation Process: 
          - Analyze the {{section_name}} section for clarity and conciseness, ensuring that it is written in straightforward language.
          - Check to confirm that the {{section_name}} section is completely aligned with the System Overview. If there seems any discrepancy call out in the validation report
          - Check to confirm that the {{section_name}} section is completely aligned with high-level requirements only if available in input.If there seems any discrepancy call out in the validation report.If High level requirement is not available or N/A then do not mention anything about high level requirements if provided in the validation report.
          - Utilize the validation checklist to assess the overall quality and compliance score by considering the weightage provided in the checklist
          - Ensure that you provide validation feedback on each checklist point and provide it in the validation report summary 
          - Explicitly call out each checklist point in the validation report findings along with the observations. Mention the checklist point as well along with the response as 'Yes' or 'No'
          - For each response, note the specific area needing improvement and suggest actionable steps for enhancement as applicable
          Output of validation process
            Validation Report: 
            - Summary: Provide a brief summary of the overall effectiveness of the {{section_name}} section based on alignment with system overview and completeness 
            - Findings: List specific findings from the validation that highlight areas of concern by providing exact text from the input. Explicitly provide the observations against each point in the validation checklist 
            - Compliance Status: Indicate whether the {{section_name}} section meets the criteria set out in the validation checklist (e.g., Fully Compliant, Partially Compliant, Non-Compliant).
            Improvement Suggestions: 
            - Ensure to provide specific recommendations for enhancing the section content based on the validation findings. 
            - Suggestions should focus on clarity, completeness, and alignment.
            - Strictly ensure for each "No" response to the checklist, note the specific area needing improvement and suggest actionable steps for enhancement


        Strictly DO NOT USE any special characters like '#' or '*' in any output content.
        {
          validation_report: validation report from validation process. Strictly ensure no special characters like '#' or '*' are present anywhere in the validation report content
          improvement_suggestions: improvement suggestions from validation process. Strictly ensure no special characters like '#' or '*' are present in the Improvement suggestions
        }

        Application_name-
        {{application_name}}

        Document Type -
        {{document_type}}

        Section name-
        {{section_name}}

        Section content-
        {{section_content}}

        System Overview-
        {{system_overview}}

        High-Level Requirements-
        {{high_level_requirements}}

        Validation checklist -
          Checklist.get_check_list({{document_type}}, {{section_name}})

      agent: QASpecialistAgent
