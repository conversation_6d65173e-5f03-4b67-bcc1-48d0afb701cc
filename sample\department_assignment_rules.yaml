OUTPUT FORMAT:
      _CASE_KEY (Keep Unchanged from input)
      _INVOICE_KEY (Keep Unchanged from input)
      VENDOR_CODE (Keep Unchanged from input)
      PAYMENT_METHOD (Keep Unchanged from input)
      RISK_TEXT (Keep Unchanged from input)
      AGENT_IDENTIFIED_RISK (Keep Unchanged from input)
      AGENT_COMMNET (Keep Unchanged from input)
      AGENT_RECOMMENDED_CHANGE (Keep Unchanged from input)
      assigned_department (Add department name based on below rules)


departments:
  - name: Finance Team
    defect_types:
      - Payment Details Change
      - Unauthorized Access
  - name: Notification and Task Assignment Team
    defect_types:
      - System Outage
      - Data Breach
  - name: Verification and Validation Team
    defect_types:
      - Payment Details Change
      - Data Integrity Issue
  - name: Feedback and Improvement Team
    defect_types:
      - Process Improvement
      - User Feedback
 
defect_handling:
  default_department: General Support Team
  assignment_rules:
    - defect_type: Payment Details Change
      department: Finance Team
    - defect_type: Unauthorized Access
      department: Risk Assessment Team
    - defect_type: System Outage
      department: Notification and Task Assignment Team
    - defect_type: Data Breach
      department: Notification and Task Assignment Team
    - defect_type: Data Integrity Issue
      department: Verification and Validation Team
    - defect_type: Process Improvement
      department: Feedback and Improvement Team
    - defect_type: User Feedback
      department: Feedback and Improvement Team