import requests
import os
import json
import xmltodict
from semantic_kernel.functions.kernel_function_decorator import kernel_function
from sk_agents.ska_types import BasePlugin
from ska_utils import StandardDates
import httpx
import asyncio


class VulnerabilityPlugin(BasePlugin):
    api_base_url = os.environ.get("TA_MICROSOFT_API")

    async def call_microsoft_api(self, text):
        url = f"{self.api_base_url}{text}"
        try:
            # print(url)
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url
                )
                data_dict = xmltodict.parse(response._content)
                # print(json.dumps(data_dict))
                return data_dict
        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    async def parse_microsoft_data(self, data):
        parsed_array = []
        try:
            if data["cvrf:cvrfdoc"]:
                current_doc = data["cvrf:cvrfdoc"]
                # print(json.dumps(current_doc))
                if current_doc["vuln:Vulnerability"]:
                    vulnerabilities = current_doc["vuln:Vulnerability"]
                    # print(json.dumps(vulnerabilities))
                    for index, item in enumerate(vulnerabilities):
                        # print(item)
                        if index >= 10:  # Stop after processing 10 items
                            break
                        new_item = {
                            "Title": item["vuln:Title"],
                            "ID": item["vuln:CVE"],
                            "url": f"https://cve.mitre.org/cgi-bin/cvename.cgi?name={item['vuln:CVE']}",
                        }
                        parsed_array.append(new_item)
            return parsed_array
        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    @kernel_function(
        description="query the microsoft security api for known issues, text should be formatted like 2025-Feb"
    )
    async def get_security_report(self, text: str):
        try:
            data = await self.call_microsoft_api(text)
            parsed_data = await self.parse_microsoft_data(data)
            # print(parsed_data)
            return parsed_data
        except Exception as error:
            print(f"error encounter: {error}")
            raise error

    @kernel_function(description="returns current month and year")
    async def get_current_month_date(self):
        dates = StandardDates()
        month = dates.get_current_month_full_name()
        year = dates.get_current_year()
        return month, year
