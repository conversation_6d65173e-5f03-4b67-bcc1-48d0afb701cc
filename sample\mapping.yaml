order_extraction_rules:
  description: "Reglas para extraer datos importantes del pedido del contenido del correo electrónico."
  rules:
    - field: "from"
      description: "Nombre y correo electrónico del remitente."
      extraction_method: "Extraer el nombre y el correo electrónico del remitente del campo 'De:'."
      subfields:
        - name: "name"
          extraction_method: "Extraer el nombre antes de la dirección de correo electrónico."
        - name: "email"
          extraction_method: "Extraer la dirección de correo electrónico entre corchetes angulares."
    - field: "order_details"
      description: "Información clave sobre el pedido."
      extraction_method: "Extraer los detalles del pedido del contenido del correo electrónico."
      subfields:
        - name: "order_number"
          extraction_method: "Extraer el número de pedido del campo relevante."
        - name: "order_date"
          extraction_method: "Extraer la fecha del pedido del campo relevante."
        - name: "delivery_date"
          extraction_method: "Extraer la fecha de entrega del campo relevante."
        - name: "client"
          extraction_method: "Extraer el nombre del cliente del campo relevante."
        - name: "destination"
          extraction_method: "Extraer el destino de entrega del campo relevante."
        - name: "delivery_address"
          extraction_method: "Extraer la dirección de entrega del campo relevante."
        - name: "payer_code"
          extraction_method: "Extraer el código del pagador del campo relevante."
        - name: "sold_to_code"
          extraction_method: "Extraer el código vendido a del campo relevante."
        - name: "ship_to_code"
          extraction_method: "Extraer el código de envío a del campo relevante."
        - name: "total_value"
          extraction_method: "Extraer el valor total del campo relevante."
    - field: "items"
      description: "Lista de artículos incluidos en el pedido."
      extraction_method: "Extraer los detalles de los artículos del contenido del correo electrónico."
      subfields:
        - name: "description"
          extraction_method: "Extraer la descripción del artículo."
        - name: "quantity"
          extraction_method: "Extraer la cantidad del artículo."
        - name: "unit_value"
          extraction_method: "Extraer el valor unitario del artículo."
        - name: "total_value"
          extraction_method: "Extraer el valor total del artículo."
        - name: "material_id"
          extraction_method: "Extraer el material_id/COD PARA GRABAR(around 7 digit number) del artículo."

    # - field: "instructions"
    #   description: "Instrucciones especiales relacionadas con el pedido."
    #   extraction_method: "Extraer cualquier instrucción especial del contenido del correo electrónico."
    # - field: "additional_info"
    #   description: "Cualquier par clave-valor adicional relacionado con el pedido."
    #   extraction_method: "Extraer información adicional del contenido del correo electrónico."



# order_extraction_rules:
#   description: "Rules for extracting important order data from email content."
#   rules:
#     - field: "from"
#       description: "Sender's name and email."
#       extraction_method: "Extract the sender's name and email from 'From:' field."
#       subfields:
#         - name: "name"
#           extraction_method: "Extract the name before the email address."
#         - name: "email"
#           extraction_method: "Extract the email address within angle brackets."
#     - field: "order_details"
#       description: "Key information about the order."
#       extraction_method: "Extract order details from the email content."
#       subfields:
#         - name: "order_number"
#           extraction_method: "Extract the order number from the relevant field."
#         - name: "order_date"
#           extraction_method: "Extract the order date from the relevant field."
#         - name: "delivery_date"
#           extraction_method: "Extract the delivery date from the relevant field."
#         - name: "client"
#           extraction_method: "Extract the client name from the relevant field."
#         - name: "destination"
#           extraction_method: "Extract the delivery destination from the relevant field."
#         - name: "delivery_address"
#           extraction_method: "Extract the delivery address from the relevant field."
#         - name: "payer_code"
#           extraction_method: "Extract the payer code from the relevant field."
#         - name: "sold_to_code"
#           extraction_method: "Extract the sold-to code from the relevant field."
#         - name: "ship_to_code"
#           extraction_method: "Extract the ship-to code from the relevant field."
#         - name: "total_value"
#           extraction_method: "Extract the total value from the relevant field."
#     - field: "items"
#       description: "List of items included in the order."
#       extraction_method: "Extract item details from the email content."
#       subfields:
#         - name: "description"
#           extraction_method: "Extract the item description."
#         - name: "quantity"
#           extraction_method: "Extract the quantity of the item."
#         - name: "unit_value"
#           extraction_method: "Extract the unit value of the item."
#         - name: "total_value"
#           extraction_method: "Extract the total value of the item."
#     - field: "instructions"
#       description: "Special instructions related to the order."
#       extraction_method: "Extract any special instructions from the email content."
#     - field: "additional_info"
#       description: "Any additional key-value pairs related to the order."
#       extraction_method: "Extract additional information from the email content."




# mappings:
#   - field: "order_id"
#     pattern: "Order (Number|ID):\\s*(\\d+)"
 
#   - field: "customer_name"
#     pattern: "Customer (Name|):\\s*([A-Za-z ,.'-]+)"
 
#   - field: "product"
#     pattern: "Product(Name)?:\\s*([A-Za-z0-9 -]+)"
 
#   - field: "quantity"
#     pattern: "Quantity:\\s*(\\d+)"
 
#   - field: "order_date"
#     pattern: "Order Date:\\s*([\\d-]+)"
 
#   - field: "total_amount"
#     pattern: "Total Amount:\\s*\\$([\\d.,]+)"
 
#   - field: "delivery_date"
#     pattern: "Delivery Date:\\s*([\\d-]+)"
 
#   - field: "shipping_address"
#     pattern: "Shipping Address:\\s*(.*)"
 
#   - field: "payment_method"
#     pattern: "Payment Method:\\s*(\\w+)"
 
#   - field: "email"
#     pattern: "Email:\\s*([\\w\\.-]+@[\\w\\.-]+)"
 
#   - field: "phone_number"
#     pattern: "Phone(?: Number)?:\\s*(\\+?\\d[\\d -]{7,})"
 
#   - field: "invoice_number"
#     pattern: "Invoice (Number|#):\\s*(\\d+)"
 
#   - field: "billing_address"
#     pattern: "Billing Address:\\s*(.*)"
 
#   - field: "shipping_method"
#     pattern: "Shipping Method:\\s*([A-Za-z ]+)"
 
#   - field: "estimated_delivery"
#     pattern: "Estimated Delivery(?: Date)?:\\s*([\\d-]+)"
 
#   - field: "tracking_number"
#     pattern: "Tracking Number:\\s*([A-Z0-9]+)"
 
#   - field: "discount"
#     pattern: "Discount(?: Amount)?:\\s*\\$([\\d.,]+)"
 
#   - field: "tax"
#     pattern: "Tax(?: Amount)?:\\s*\\$([\\d.,]+)"
 
#   - field: "subtotal"
#     pattern: "Subtotal:\\s*\\$([\\d.,]+)"
 
#   - field: "payment_status"
#     pattern: "Payment Status:\\s*(\\w+)"
 
#   - field: "customer_feedback"
#     pattern: "Customer Feedback:\\s*(.*)"
 
#   - field: "order_status"
#     pattern: "Order Status:\\s*(\\w+)"
 
#   - field: "refund_amount"
#     pattern: "Refund Amount:\\s*\\$([\\d.,]+)"
 
#   - field: "return_policy"
#     pattern: "Return Policy:\\s*(.*)"
 
#   - field: "contact_person"
#     pattern: "Contact Person:\\s*([A-Za-z ,.'-]+)"
 
#   - field: "company_name"
#     pattern: "Company Name:\\s*([A-Za-z ,.'-]+)"
 
#   - field: "purchase_order"
#     pattern: "Purchase Order:\\s*(\\d+)"
 
#   - field: "warranty_period"
#     pattern: "Warranty Period:\\s*([\\d]+ months)"
 
#   - field: "service_contact"
#     pattern: "Service Contact:\\s*([A-Za-z ,.'-]+)"
 
#   - field: "support_email"
#     pattern: "Support Email:\\s*([\\w\\.-]+@[\\w\\.-]+)"
 
#   - field: "support_phone"
#     pattern: "Support Phone:\\s*(\\+?\\d[\\d -]{7,})"
 
#   - field: "contract_number"
#     pattern: "Contract Number:\\s*(\\d+)"
 
#   - field: "project_name"
#     pattern: "Project Name:\\s*([A-Za-z0-9 -]+)"
 
#   - field: "project_manager"
#     pattern: "Project Manager:\\s*([A-Za-z ,.'-]+)"