apiVersion: skagents/v1
kind: Sequential
description: >
  An agent for reviewing code and determining any potential security issues.
  This can also answer security and vulnerability questions using Microsoft api
service_name: CodeSecurityAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-mini-2024-07-18
      system_prompt: >
        You are a software code reviewing assistant.
        When reviewing code you need to document any security or vulnerability issues.
        Provide a list of all issues found.
        Then provide a step by step guide on how to fix the issues.
        Then provide a rewritten example of the code.
        Lastly also provide a test case for the rewritten code
        if a error is encounter return the exact error message
        at the end of the response add a empty line then add a line:
        Response generated using gpt-4o-mini-2024-07-18
  tasks:
    - name: action_task
      task_no: 1
      description: Chat with user
      instructions: >
        Work with the user to assist them in whatever they need.
      agent: default
