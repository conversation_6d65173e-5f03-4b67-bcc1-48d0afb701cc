# Email Classifying Agent README

## 1. Overview
The EmailClassifyingAgent processes email data, extracts relevant fields, and classifies emails as either "order" or "not order" based on predefined rules.

## 2. Features
- Extracts and maps email data according to predefined rules.
- Classifies emails as either "order" or "not order".
- Returns results in a structured JSON format.
- Logs classification activities for monitoring and debugging.

## 3. Dependencies
This agent depends on the following files:
- **Main Agent File**: `gbsdsai-agent-configs/agent1_uc1.py`
- **Configuration File**: `EmailClassifyingAgent/config.yaml`
- **Rules File**: `../sample/order_rules.yaml`
- **AWS Connector File**: `gbsdsai-agent-configs\aws_connector.py`


### Environment Variables
A `.env` file needs to be created at `EmailClassifyingAgent/.env` with the following required values:

TA_API_KEY="Use your KEY here"
TA_SERVICE_CONFIG=EmailClassifyingAgent/config.yaml
TA_BASE_URL=https://iapi-test.merck.com/gpt/libsupport
TA_TELEMETRY_ENABLE=false
TA_CUSTOM_CHAT_COMPLETION_FACTORY_MODULE=misc/merck_custom_chat_completion_factory.py
TA_CUSTOM_CHAT_COMPLETION_FACTORY_CLASS_NAME=MerckCustomChatCompletionFactory
TA_STRUCTURED_OUTPUT_TRANSFORMER_MODEL=gpt-4o-2024-08-06

### How the Agent Works

### Configuration Loading
1. Loads configuration from EmailClassifyingAgent/config.yaml.
2. Defines the agent's structure, input types, and processing agents.

### Plugin Initialization
1. Imports the custom_plugins file.
2. Initializes the EmailClassifyingPlugin class.
3. Loads classification rules from the specified rules file.

### AWS Connector
1. Utilizes the aws_connector.py file to read email and attachment data from Amazon S3.
2. The readS3() function retrieves and processes the data, converting it into a structured JSON format.
3. Environment variables for AWS credentials and S3 bucket information are loaded from a main .env file.
4. The processed data is then used for classification by the Email Classifying Agent.  

### Email Processing
1. Upon receiving an email, the classify_email function is invoked within the EmailClassifyingPlugin.
2. Analyzes the email content based on the loaded classification rules.
3. Constructs a prompt for the Azure OpenAI model to classify the email.
4. Calls the OpenAI API with the constructed prompt.

### Result Generation
1. Returns the classification result in a structured JSON format.
2. Includes the classification (e.g., "order" or "not order") and the original email data.

### Logging
1. Creates a log file in the /sample/ directory for each run.
2. Log file is named using the format agent_log_YYMMDD+time (e.g., agent_log_2025-05-29_12-43-57).

### Usage
To run the agent, follow these steps:
1. Navigate to the main folder.
2. Activate the virtual environment using the following command: .venv/scripts/activate
3. Run the agent with the command: python gbsdsai-agent-configs/agent1_uc1.py

### Sample Output
{
    "classification": "order",
    "email_data": {
      "email_id": "20250519_164933",
      "email_data": [
        {
          "key": "Subject:",
          "Key_confidence_score": 93.63616180419922,
          "value": "Orden de Compra 13373397 - BOGOTA-MERCK SHARP & DOHME COLOMBIA SAS - 2025-05-09",
          "Value_confidence_score": 93.63616180419922
        },
        {
          "key": "VALOR C/U",
          "Key_confidence_score": 95.17568969726562,
          "value": "20,929,888",
          "Value_confidence_score": 95.17568969726562
        },
        {
          "key": "Date:",
          "Key_confidence_score": 93.14763641357422,
          "value": "Tuesday, May 6, 2025 2:29:33 PM",
          "Value_confidence_score": 93.14763641357422
        },
        {
          "key": "COD SOLD-TO",
          "Key_confidence_score": 94.89192199707033,
          "value": "50084863",
          "Value_confidence_score": 94.89192199707033
        },
        {
          "key": "ORDEN DE COMPRA",
          "Key_confidence_score": 95.12344360351562,
          "value": "13373397",
          "Value_confidence_score": 95.12344360351562
        },
        {
          "key": "CLIENTE",
          "Key_confidence_score": 94.52747344970705,
          "value": "CRUZ VERDE_INST",
          "Value_confidence_score": 94.52747344970705
        },
        {
          "key": "Cc:",
          "Key_confidence_score": 92.97449493408205,
          "value": "<EMAIL>",
          "Value_confidence_score": 92.97449493408205
        },
        {
          "key": "To:",
          "Key_confidence_score": 94.23657989501952,
          "value": "GBS Digital Services AI",
          "Value_confidence_score": 94.23657989501952
        },
        {
          "key": "INSTRUCCI\u00d3N",
          "Key_confidence_score": 94.78551483154295,
          "value": "052 CITA PREVIA",
          "Value_confidence_score": 94.78551483154295
        },
        {
          "key": "To:",
          "Key_confidence_score": 94.48270416259766,
          "value": "ColombiaBSCSO <<EMAIL>>",
          "Value_confidence_score": 94.48270416259766
        },
        {
          "key": "VALOR TOTAL",
          "Key_confidence_score": 94.85872650146484,
          "value": "$ 293,018,432",
          "Value_confidence_score": 94.85872650146484
        },
        {
          "key": "Subject:",
          "Key_confidence_score": 93.62747955322266,
          "value": "Orden de Compra 13373397 BOGOTA-MERCK SHARP & DOHME COLOMBIA SAS - 2025-05-09",
          "Value_confidence_score": 93.62747955322266
        },
        {
          "key": "COD PARA GRABAR",
          "Key_confidence_score": 95.01252746582033,
          "value": "1041849",
          "Value_confidence_score": 95.01252746582033
        },
        {
          "key": "DESTINO",
          "Key_confidence_score": 94.67037200927734,
          "value": "COTA INSTITUCIONAL - CEDI ORION LOTE SAN GREGORIO COTA",
          "Value_confidence_score": 94.67037200927734
        },
        {
          "key": "To:",
          "Key_confidence_score": 85.2539291381836,
          "value": "Duarte Montero, Fernando <<EMAIL>>; Garzon Munoz, Kevin Steven <<EMAIL>>",
          "Value_confidence_score": 85.2539291381836
        },
        {
          "key": "COD PAYER",
          "Key_confidence_score": 95.03353118896484,
          "value": "3000009063",
          "Value_confidence_score": 95.03353118896484
        },
        {
          "key": "Fecha Cita Entrega",
          "Key_confidence_score": 94.8255844116211,
          "value": "viernes, 09 de mayo de 2025",
          "Value_confidence_score": 94.8255844116211
        },
        {
          "key": "Subject:",
          "Key_confidence_score": 93.21794128417967,
          "value": "Orden de Compra 13373397 BOGOTA-MERCK SHARP & DOHME COLOMBIA SAS 2025-05-09",
          "Value_confidence_score": 93.21794128417967
        },
        {
          "key": "VALOR TOTAL",
          "Key_confidence_score": 95.10182189941406,
          "value": "293,018,432",
          "Value_confidence_score": 95.10182189941406
        },
        {
          "key": "From:",
          "Key_confidence_score": 94.60199737548828,
          "value": "Demel, Milan",
          "Value_confidence_score": 94.60199737548828
        },
        {
          "key": "From:",
          "Key_confidence_score": 94.5994873046875,
          "value": "Garzon Munoz, Kevin Steven <<EMAIL>>",
          "Value_confidence_score": 94.5994873046875
        },
        {
          "key": "COD SHIP TO",
          "Key_confidence_score": 94.85953521728516,
          "value": "50084863",
          "Value_confidence_score": 94.85953521728516
        },
        {
          "key": "Sent:",
          "Key_confidence_score": 94.99671173095705,
          "value": "Monday, May 5, 2025 10:17 PM",
          "Value_confidence_score": 94.99671173095705
        },
        {
          "key": "From:",
          "Key_confidence_score": 94.74177551269533,
          "value": "OrderManager <<EMAIL>>",
          "Value_confidence_score": 94.74177551269533
        },
        {
          "key": "Sent:",
          "Key_confidence_score": 95.14453125,
          "value": "Friday, 2 May, 2025 6:29 PM",
          "Value_confidence_score": 95.14453125
        },
        {
          "key": "CANT",
          "Key_confidence_score": 95.05833435058594,
          "value": "14",
          "Value_confidence_score": 95.05833435058594
        },
        {
          "key": "Attachments:",
          "Key_confidence_score": 94.0106964111328,
          "value": "Orden de Compra.pdf image001.png image002.pna",
          "Value_confidence_score": 94.0106964111328
        },
        {
          "key": "DESCRIPCION",
          "Key_confidence_score": 94.15216064453124,
          "value": "PREVYMIS 240MG 4X7TAB COL",
          "Value_confidence_score": 94.15216064453124
        },
        {
          "key": "FW:",
          "Key_confidence_score": 54.60276794433594,
          "value": null,
          "Value_confidence_score": 54.60276794433594
        },
        {
          "key": "EXTERNAL EMAIL-",
          "Key_confidence_score": 49.71565246582031,
          "value": "Use caution with any links or file attachments.",
          "Value_confidence_score": 49.71565246582031
        },
        {
          "key": "de abril de",
          "Key_confidence_score": 49.2802619934082,
          "value": "2022.",
          "Value_confidence_score": 49.2802619934082
        },
        {
          "key": "Hora:",
          "Key_confidence_score": 89.804931640625,
          "value": "11:00",
          "Value_confidence_score": 89.804931640625
        },
        {
          "key": "Cordialmente;",
          "Key_confidence_score": 82.87442016601562,
          "value": "Equipo de Compras",
          "Value_confidence_score": 82.87442016601562
        },
        {
          "key": "Direcci\u00f3n de Entrega",
          "Key_confidence_score": 94.88208770751952,
          "value": "Centro de Distribuci\u00f3n Orion - Parque Log\u00edstico Constellation, Vereda Vuelta Grande Km 1 v\u00eda potrero chico 1 COLOMBIA-COTA",
          "Value_confidence_score": 94.88208770751952
        },
        {
          "key": "Fecha:",
          "Key_confidence_score": 91.41717529296876,
          "value": "2025-05-09",
          "Value_confidence_score": 91.41717529296876
        }
      ],
      "attachment_data": [
        {
          "Page Number": 1.0,
          "Key": "Proveedor:",
          "Value": "BOGOTA-MERCK SHARP & DOHME COLOMBIA SAS",
          "Key Confidence": 94.64997863769533,
          "Value Confidence": 94.64997863769533
        },
        {
          "Page Number": 1.0,
          "Key": "Sucursal:",
          "Value": "BOGOTA",
          "Key Confidence": 94.86873626708984,
          "Value Confidence": 94.86873626708984
        },
        {
          "Page Number": 1.0,
          "Key": "SUB TOTAL:",
          "Value": "293.018.432",
          "Key Confidence": 94.7948989868164,
          "Value Confidence": 94.7948989868164
        },
        {
          "Page Number": 1.0,
          "Key": "Contacto:",
          "Value": "CAROLINA CASTRO",
          "Key Confidence": 94.89185333251952,
          "Value Confidence": 94.89185333251952
        },
        {
          "Page Number": 1.0,
          "Key": "%IVA",
          "Value": "0%",
          "Key Confidence": 95.14689636230467,
          "Value Confidence": 95.14689636230467
        },
        {
          "Page Number": 1.0,
          "Key": "FAX:",
          "Value": "4924860",
          "Key Confidence": 94.8682632446289,
          "Value Confidence": 94.8682632446289
        },
        {
          "Page Number": 1.0,
          "Key": "Direcci\u00f3n:",
          "Value": "Centro de Distribuci\u00f3n Orion Parque Log\u00edstico Constellation Vereda Vuelta Grande Km 1 v\u00eda potrero chico 1 COLOMBIA-COTA",
          "Key Confidence": 94.1359634399414,
          "Value Confidence": 94.1359634399414
        },
        {
          "Page Number": 1.0,
          "Key": "Se\u00f1or Proveedor, recuerde:",
          "Value": null,
          "Key Confidence": 88.2825698852539,
          "Value Confidence": 88.2825698852539
        },
        {
          "Page Number": 1.0,
          "Key": "Fecha OC:",
          "Value": "2025-05-02",
          "Key Confidence": 95.31964874267578,
          "Value Confidence": 95.31964874267578
        },
        {
          "Page Number": 1.0,
          "Key": "IVA TOTAL:",
          "Value": null,
          "Key Confidence": 91.89688873291016,
          "Value Confidence": 91.89688873291016
        },
        {
          "Page Number": 1.0,
          "Key": "Elaborado por:",
          "Value": "MARIA.PENAR",
          "Key Confidence": 94.07272338867188,
          "Value Confidence": 94.07272338867188
        },
        {
          "Page Number": 1.0,
          "Key": "Mto.IVA",
          "Value": null,
          "Key Confidence": 91.57093048095705,
          "Value Confidence": 91.57093048095705
        },
        {
          "Page Number": 1.0,
          "Key": "Observaciones:",
          "Value": "02 05 2025",
          "Key Confidence": 93.08187103271484,
          "Value Confidence": 93.08187103271484
        },
        {
          "Page Number": 1.0,
          "Key": "Fecha Elaboraci\u00f3n:",
          "Value": "2025-05-02",
          "Key Confidence": 95.09464263916016,
          "Value Confidence": 95.09464263916016
        },
        {
          "Page Number": 1.0,
          "Key": "Unids.",
          "Value": "CAJ",
          "Key Confidence": 94.9948501586914,
          "Value Confidence": 94.9948501586914
        },
        {
          "Page Number": 1.0,
          "Key": "Direcci\u00f3n:",
          "Value": "CL 127 a 53 A 45 TORRE 3 PISO 8",
          "Key Confidence": 94.49906158447266,
          "Value Confidence": 94.49906158447266
        },
        {
          "Page Number": 1.0,
          "Key": "Nit:",
          "Value": "800149695-1",
          "Key Confidence": 94.1523666381836,
          "Value Confidence": 94.1523666381836
        },
        {
          "Page Number": 1.0,
          "Key": "Tel\u00e9fono:",
          "Value": "4924860",
          "Key Confidence": 94.98129272460938,
          "Value Confidence": 94.98129272460938
        },
        {
          "Page Number": 1.0,
          "Key": "Comprador:",
          "Value": "Pena Forero, Maria Alejandra",
          "Key Confidence": 95.13069152832033,
          "Value Confidence": 95.13069152832033
        },
        {
          "Page Number": 1.0,
          "Key": "Bodega:",
          "Value": "1058 CRUZVERDE CEDI_COTA ALMACEN_PRINCIPAL",
          "Key Confidence": 93.37238311767578,
          "Value Confidence": 93.37238311767578
        },
        {
          "Page Number": 1.0,
          "Key": "TOTALES:",
          "Value": "293.018.432",
          "Key Confidence": 94.53596496582033,
          "Value Confidence": 94.53596496582033
        },
        {
          "Page Number": 1.0,
          "Key": "Art\u00edculo",
          "Value": "547522",
          "Key Confidence": 94.7558822631836,
          "Value Confidence": 94.7558822631836
        },
        {
          "Page Number": 1.0,
          "Key": "Descripci\u00f3n",
          "Value": "PREVYMIS 240MG TAB REC CAJ X 28",
          "Key Confidence": 93.44135284423828,
          "Value Confidence": 93.44135284423828
        },
        {
          "Page Number": 1.0,
          "Key": "Costo Total",
          "Value": "293.018.432",
          "Key Confidence": 94.24774932861328,
          "Value Confidence": 94.24774932861328
        },
        {
          "Page Number": 1.0,
          "Key": "Direcci\u00f3n:",
          "Value": "Cra 12 # 96-32",
          "Key Confidence": 94.75299835205078,
          "Value Confidence": 94.75299835205078
        },
        {
          "Page Number": 1.0,
          "Key": "Precio Unitario F.",
          "Value": "20.929.888",
          "Key Confidence": 94.1620101928711,
          "Value Confidence": 94.1620101928711
        },
        {
          "Page Number": 1.0,
          "Key": "Unids. Solicit",
          "Value": "14",
          "Key Confidence": 94.97534942626952,
          "Value Confidence": 94.97534942626952
        },
        {
          "Page Number": 1.0,
          "Key": "Costo",
          "Value": "293.018.432",
          "Key Confidence": 94.96345520019533,
          "Value Confidence": 94.96345520019533
        },
        {
          "Page Number": 1.0,
          "Key": "Fecha:",
          "Value": "2025-05-09",
          "Key Confidence": 89.99102020263672,
          "Value Confidence": 89.99102020263672
        },
        {
          "Page Number": 1.0,
          "Key": "Tel\u00e9fono:",
          "Value": null,
          "Key Confidence": 92.15746307373048,
          "Value Confidence": 92.15746307373048
        },
        {
          "Page Number": 1.0,
          "Key": "Moneda:",
          "Value": "COP",
          "Key Confidence": 95.10894775390624,
          "Value Confidence": 95.10894775390624
        },
        {
          "Page Number": 1.0,
          "Key": "Aprobado por:",
          "Value": "adriana.osorio",
          "Key Confidence": 94.45423126220705,
          "Value Confidence": 94.45423126220705
        },
        {
          "Page Number": 1.0,
          "Key": "Cantidad (Recibo)",
          "Value": null,
          "Key Confidence": 91.32650756835938,
          "Value Confidence": 91.32650756835938
        },
        {
          "Page Number": 1.0,
          "Key": "Forma de Pago:",
          "Value": "PAGO A 120 DIAS",
          "Key Confidence": 95.1484375,
          "Value Confidence": 95.1484375
        },
        {
          "Page Number": 1.0,
          "Key": "C\u00f3digo CR 11 Co",
          "Value": "547522",
          "Key Confidence": 92.6554946899414,
          "Value Confidence": 92.6554946899414
        },
        {
          "Page Number": 1.0,
          "Key": "Hora:",
          "Value": "11:00",
          "Key Confidence": 89.16079711914062,
          "Value Confidence": 89.16079711914062
        },
        {
          "Page Number": 1.0,
          "Key": "Nit:",
          "Value": "860002392-1",
          "Key Confidence": 94.5708999633789,
          "Value Confidence": 94.5708999633789
        },
        {
          "Page Number": 1.0,
          "Key": "Sr.",
          "Value": "La orden de compra est\u00e1 vigente \u00fanicamente hasta la fecha pactada Entrega, en caso de incumplimiento queda cerrado el pedido. En el caso de medicamentos tenga en cuenta que no se recibe mercanc\u00eda con menos de 12 meses de vida \u00fatil. Al entregar esta orden de compra el proveedor da por aceptado los t\u00e9rminos y condiciones relacionados en la misma (tarifa, t\u00e9rmino de pago y punto de entrega, entre otros.) El t\u00e9rmino de pago relacionado en la presente orden de compra inicia a correr a partir de la correcta radicaci\u00f3n de sus facturas. Si la descripci\u00f3n del producto contiene la sigla INST el producto deber\u00e1 in marcado \"Uso Institucional\", de lo contrario se generar\u00e1 devoluci\u00f3n. Cruz Verde no adquirir\u00e1 ni recibir\u00e1 productos con registro sanitario no vigente, en caso de recepci\u00f3n en el CEDI o en alg\u00fan punto, Cruz Verde se reserva el derecho a su devoluci\u00f3n.",
          "Key Confidence": 45.439552307128906,
          "Value Confidence": 45.439552307128906
        },
        {
          "Page Number": 1.0,
          "Key": "1 de",
          "Value": "1",
          "Key Confidence": 48.06734085083008,
          "Value Confidence": 48.06734085083008
        },
        {
          "Page Number": 1.0,
          "Key": "Pedido General",
          "Value": null,
          "Key Confidence": 55.45286560058594,
          "Value Confidence": 55.45286560058594
        },
        {
          "Page Number": 1.0,
          "Key": "N\u00b0 ORDEN DE COMPRA",
          "Value": "13373397",
          "Key Confidence": 70.75467681884766,
          "Value Confidence": 70.75467681884766
        }
      ]
    }
  }

### Testing
1. The agent includes a suite of unit tests located at EmailClassifyingAgent/tests/test_custom_plugins.py.
2. The tests verify the functionality of the classify_email method in the EmailClassifyingPlugin class.
   Two primary tests are included:
-  Positive Test: Confirms that emails containing order-related information are classified as "order".
-  Negative Test: Ensures that emails without order-related information are classified as "not order".
   To run the tests, follow these steps:   
1. Navigate to the EmailClassifyingAgent folder.
2. execute the following command in the terminal:
  `python -m unittest -v .\tests\test_custom_plugins.py`

### API Access
Once running, access the Swagger documentation at:
http://localhost:5000/EmailClassifyingAgent/0.1/docs