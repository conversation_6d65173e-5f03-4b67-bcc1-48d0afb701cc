from typing import List, Dict

from anthropic import AsyncAnthropic
from semantic_kernel.connectors.ai.anthropic.services.anthropic_chat_completion import (
    AnthropicChatCompletion,
)
from semantic_kernel.connectors.ai.chat_completion_client_base import (
    ChatCompletionClientBase,
)
from semantic_kernel.connectors.ai.open_ai.services.azure_chat_completion import (
    AzureChatCompletion,
)
from ska_utils import Config as UtilConfig, AppConfig

from sk_agents.configs import TA_API_KEY
from sk_agents.ska_types import ChatCompletionFactory, ModelType


class MerckCustomChatCompletionFactory(ChatCompletionFactory):
    _MSD_OPENAI_MODELS: Dict[str, str] = {
        "gpt-35-turbo-0125": "gpt-35-turbo-0125",
        "gpt-4o-2024-05-13": "gpt-4o-2024-05-13",
        "gpt-4o-2024-08-06": "gpt-4o-2024-08-06",
        "gpt-4o-2024-11-20": "gpt-4o-2024-11-20",
        "gpt-4o-mini-2024-07-18": "gpt-4o-mini-2024-07-18",
        "gpt-4-turbo-2024-04-09": "gpt-4-turbo-2024-04-09",
        "o1-2024-12-17": "o1-2024-12-17",
        "o3-mini-2025-01-31": "o3-mini-2025-01-31",
        "gpt-4o": "gpt-4o-2024-11-20",
        "gpt-4o-mini": "gpt-4o-mini-2024-07-18",
    }
    _ANTHROPIC_MODELS: List[str] = [
        "claude-3-5-sonnet-20240620-v1",
        "claude-3-5-sonnet-20241022-v2",
        "claude-3-5-haiku-20241022-v1",
        "claude-3-7-sonnet-20250219-v1",
    ]

    TA_BASE_URL = UtilConfig(
        env_name="TA_BASE_URL",
        is_required=True,
        default_value="https://iapi-test.merck.com/gpt/libsupport",
    )
    TA_API_VERSION = UtilConfig(
        env_name="TA_API_VERSION", is_required=False, default_value="2025-03-01-preview"
    )

    _CONFIGS: List[UtilConfig] = [TA_BASE_URL, TA_API_VERSION]

    def __init__(self, app_config: AppConfig):
        super().__init__(app_config)
        self.api_key = app_config.get(TA_API_KEY.env_name)
        self.url_base = app_config.get(
            MerckCustomChatCompletionFactory.TA_BASE_URL.env_name
        )
        self.api_version = app_config.get(
            MerckCustomChatCompletionFactory.TA_API_VERSION.env_name
        )

    @staticmethod
    def get_configs() -> List[UtilConfig]:
        return MerckCustomChatCompletionFactory._CONFIGS

    def get_chat_completion_for_model_name(
        self, model_name: str, service_id: str
    ) -> ChatCompletionClientBase:
        if model_name in MerckCustomChatCompletionFactory._MSD_OPENAI_MODELS:
            return AzureChatCompletion(
                service_id=service_id,
                deployment_name=MerckCustomChatCompletionFactory._MSD_OPENAI_MODELS[
                    model_name
                ],
                api_key=self.api_key,
                base_url=f"{self.url_base}/openai",
                api_version=self.api_version,
            )
        elif model_name in MerckCustomChatCompletionFactory._ANTHROPIC_MODELS:
            return AnthropicChatCompletion(
                service_id=service_id,
                api_key="unused",
                ai_model_id=model_name,
                async_client=AsyncAnthropic(
                    api_key="unused",
                    base_url=f"{self.url_base}/anthropic/{model_name}",
                    default_headers={"X-Merck-APIKey": self.api_key},
                ),
            )
        raise ValueError("Model type not supported")

    def get_model_type_for_name(self, model_name: str) -> ModelType:
        if model_name in MerckCustomChatCompletionFactory._MSD_OPENAI_MODELS:
            return ModelType.OPENAI
        elif model_name in MerckCustomChatCompletionFactory._ANTHROPIC_MODELS:
            return ModelType.ANTHROPIC
        else:
            raise ValueError(f"Unknown model name {model_name}")

    def model_supports_structured_output(self, model_name: str) -> bool:
        if model_name in [
            "gpt-4o-2024-05-13",
            "gpt-4o-2024-08-06",
            "gpt-4o-2024-11-20",
            "gpt-4o-mini-2024-07-18",
            "gpt-4-turbo-2024-04-09",
            "o1-2024-12-17",
            "o3-mini-2025-01-31",
            "gpt-4o",
            "gpt-4o-mini",
        ]:
            return True
        return False
