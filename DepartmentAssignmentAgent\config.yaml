apiVersion: skagents/v1
kind: Sequential
description: >
  A Department Assignment Agent
service_name: DeptAssignment
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: default
      role: Default Agent
      model: gpt-4o-2024-08-06
      system_prompt: >
        You are a helpful assistant for assigning departments to defects based on their risk type.
        Your task is to process defect data from 'department_assignment.json' file and assign it to the appropriate department using the provided rules in 'department_assignment_rules.yaml' file.
      plugins:
        - DepartmentAssignmentPlugin
  tasks:
    - name: action_task
      task_no: 1
      description: Assign departments to defects based on their type.
      instructions: >
        1. Call the 'assign_department' function.
        2. The function will automatically load data from the input provided.
        3. Ensure that all defects are processed and assigned to the correct department.
        4. Return the updated results as a JSON array.
        5. Don't add any extra text or explanation to the JSON output.
      agent: default
  file_paths:
    DEPARTMENT_ASSIGNMENT_RULES_FILE:
    - "../sample/department_assignment_rules.yaml"
    - "../sample/department_assignment.json"