apiVersion: skagents/v1
kind: Sequential
description: >
  An agent that classifies emails as orders or not orders based on extracted content
service_name: EmailClassifyingAgent
version: 0.1
input_type: BaseInput
spec:
  agents:
    - name: email_processor
      role: Email Processing Agent
      model: gpt-4o-2024-11-20
      system_prompt: >
        You are an email processing assistant that specializes in:
        1. Processing Spanish language emails with high accuracy.
        2. Analyzing extracted content from email bodies and attachments.
        3. Classifying emails as either order-related or not order-related.
        
        Your goal is to accurately identify order-related emails based on the extracted content provided.
        
        When classifying emails:
        - Analyze the provided extracted text to identify order-related keywords and patterns
        - Classify each email as either "order" or "not order"
        - Return results in a structured JSON format
      plugins:
        - EmailClassifyingPlugin
    
  tasks:
    - name: classify_email
      task_no: 1
      description: Classify the extracted email content as order or not order
      instructions: >
        Follow these steps to classify the email:
      
        1. Call the 'classify_email' function with the extracted email data provided as input.
        
        The 'classify_email' function will:
        - Classify the email as either "order" or "not order" based on the extracted content
        - Return the classification result in the required format
        - Directly provide the Output of 'classify_email' function as it is. Do not add any extra details or explanation.
      agent: email_processor


  file_paths:
    RULES_FILE: "../sample/order_rules.yaml"





