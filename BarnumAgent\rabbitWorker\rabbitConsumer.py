import pika
import json
import redis
import requests
import os
from pika.exceptions import AMQPConnectionError
import time

redis_host = os.environ.get("TA_REDIS_HOST")
redis_port = os.environ.get("TA_REDIS_PORT")
rabbitmq_host = os.environ.get("TA_RABBITMQ_PORT")
# Connect to Redis
redis_client = redis.StrictRedis(
    host=redis_host, port=redis_port, decode_responses=True
)


def connect_to_rabbitmq():
    while True:
        try:
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(host=rabbitmq_host)
            )
            print("Connected to RabbitMQ")
            return connection
        except AMQPConnectionError:
            print("RabbitMQ not ready, retrying in 5 seconds...")
            time.sleep(5)


connection = connect_to_rabbitmq()
channel = connection.channel()

# Declare the queue
channel.queue_declare(queue="event_queue")


def callback(ch, method, properties, body):
    try:
        # Deserialize the incoming message
        event_data = json.loads(body)
        print(" [x] Received %r" % event_data)

        # Here you would normally process the event
        # For demonstration, let's assume we just want to store the user_id
        job = event_data["job"]
        url = event_data["url"]

        response = requests.get(url).json()
        hits = response.get("hits", [])
        save_data = {"hits": hits[:10]}
        # Store the result in Redis, using user_id as the key
        # You can also store multiple items in a data structure
        # (like a list) if needed
        redis_client.set(job, json.dumps(save_data))  # Store as JSON

        print(f" [*] Stored in Redis: {job} -> {response}")

        # Acknowledge the message after successfully storing the result
        ch.basic_ack(delivery_tag=method.delivery_tag)

    except Exception as e:
        print(f"Error processing message: {e}")
        # If there was an error, you can choose to reject the message
        # and possibly requeue it
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)


# Set up the consumer to listen for messages
channel.basic_consume(queue="event_queue", on_message_callback=callback)

print(" [*] Waiting for messages. To exit press CTRL+C")
channel.start_consuming()
